#!/usr/bin/env python3
"""
TEST THE DEPLOYED CONTRACT!
"""
from web3 import Web3

print("🧪 TESTING DEPLOYED CONTRACT")
print("=" * 40)

# Connect to SKALE Europa
rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
w3 = Web3(Web3.HTTPProvider(rpc_url))

# Contract address from the transaction (convert to checksum)
contract_address = w3.to_checksum_address("0xd7479d4105D9caeA106a26AF479006B6216f155a")

print(f"📍 Contract Address: {contract_address}")

# Simple ABI for our contract
contract_abi = [
    {
        "inputs": [],
        "name": "owner",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "string", "name": "id", "type": "string"}],
        "name": "get",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    }
]

# Create contract instance
contract = w3.eth.contract(address=contract_address, abi=contract_abi)

try:
    # Test 1: Check if contract exists
    code = w3.eth.get_code(contract_address)
    if code == b'':
        print("❌ No contract code found at this address")
    else:
        print(f"✅ Contract code found! Length: {len(code)} bytes")
        
        # Test 2: Call owner function
        try:
            owner = contract.functions.owner().call()
            print(f"👑 Contract Owner: {owner}")
            print(f"🎉 CONTRACT IS WORKING!")
        except Exception as e:
            print(f"⚠️ Owner call failed: {e}")
            
        # Test 3: Try to call get function
        try:
            result = contract.functions.get("test").call()
            print(f"📋 Get function works! Result: '{result}'")
        except Exception as e:
            print(f"⚠️ Get call failed: {e}")
            
except Exception as e:
    print(f"❌ Contract test failed: {e}")

print(f"\n🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{contract_address}")
