"""
Enhanced error handling and recovery mechanisms for blockchain operations
"""
import logging
import time
import traceback
from typing import Dict, Any, Optional, Callable
from functools import wraps
from web3.exceptions import Web3Exception, TransactionNotFound, BlockNotFound
from requests.exceptions import ConnectionError, Timeout, RequestException

logger = logging.getLogger(__name__)

class BlockchainError(Exception):
    """Base exception for blockchain-related errors"""
    def __init__(self, message: str, error_code: str = None, recovery_suggestions: list = None):
        super().__init__(message)
        self.error_code = error_code
        self.recovery_suggestions = recovery_suggestions or []

class ConnectionError(BlockchainError):
    """Network connection related errors"""
    pass

class ContractError(BlockchainError):
    """Smart contract related errors"""
    pass

class TransactionError(BlockchainError):
    """Transaction related errors"""
    pass

class ValidationError(BlockchainError):
    """Input validation errors"""
    pass

class ErrorHandler:
    """Centralized error handling and recovery system"""
    
    def __init__(self):
        self.error_patterns = {
            # Connection errors
            'connection_refused': {
                'pattern': ['connection refused', 'connection failed', 'network unreachable'],
                'error_type': ConnectionError,
                'recovery_suggestions': [
                    "Check your internet connection",
                    "Verify the RPC URL is correct",
                    "Try again in a few moments",
                    "Check if the SKALE Europa network is operational"
                ]
            },
            'timeout': {
                'pattern': ['timeout', 'timed out', 'request timeout'],
                'error_type': ConnectionError,
                'recovery_suggestions': [
                    "The network request timed out",
                    "Try again with a longer timeout",
                    "Check network stability",
                    "Verify RPC endpoint is responsive"
                ]
            },
            
            # Contract errors
            'contract_not_found': {
                'pattern': ['contract not found', 'no code at address', 'invalid address'],
                'error_type': ContractError,
                'recovery_suggestions': [
                    "Verify the contract address is correct",
                    "Check if the contract is deployed on this network",
                    "Redeploy the contract if necessary",
                    "Update the contract address in your configuration"
                ]
            },
            'contract_call_failed': {
                'pattern': ['execution reverted', 'call failed', 'invalid opcode'],
                'error_type': ContractError,
                'recovery_suggestions': [
                    "Check the contract function parameters",
                    "Verify the contract state allows this operation",
                    "Ensure you have the correct contract ABI",
                    "Check if the contract function exists"
                ]
            },
            
            # Transaction errors
            'insufficient_funds': {
                'pattern': ['insufficient funds', 'insufficient balance', 'not enough sFUEL'],
                'error_type': TransactionError,
                'recovery_suggestions': [
                    "Get more sFUEL tokens for your wallet",
                    "Visit the SKALE faucet to get free sFUEL",
                    "Check your wallet balance",
                    "Note: SKALE has zero gas fees, but you still need some sFUEL"
                ]
            },
            'nonce_too_low': {
                'pattern': ['nonce too low', 'invalid nonce'],
                'error_type': TransactionError,
                'recovery_suggestions': [
                    "Wait for pending transactions to complete",
                    "Reset your wallet nonce if using MetaMask",
                    "Try the transaction again",
                    "Check for stuck transactions"
                ]
            },
            'gas_limit_exceeded': {
                'pattern': ['gas limit exceeded', 'out of gas'],
                'error_type': TransactionError,
                'recovery_suggestions': [
                    "Increase the gas limit for the transaction",
                    "Optimize the contract function if possible",
                    "Break down complex operations into smaller transactions"
                ]
            },
            
            # Validation errors
            'invalid_hash': {
                'pattern': ['invalid hash', 'hash format', 'not a valid hash'],
                'error_type': ValidationError,
                'recovery_suggestions': [
                    "Ensure the hash is a valid SHA-256 (64 characters)",
                    "Check for typos in the hash",
                    "Verify the file hasn't been corrupted",
                    "Recalculate the file hash if necessary"
                ]
            },
            'invalid_address': {
                'pattern': ['invalid address', 'address format', 'not a valid address'],
                'error_type': ValidationError,
                'recovery_suggestions': [
                    "Ensure the address starts with '0x'",
                    "Check that the address is 42 characters long",
                    "Verify the address checksum",
                    "Use a valid Ethereum address format"
                ]
            }
        }
    
    def classify_error(self, error: Exception) -> Dict[str, Any]:
        """Classify an error and provide recovery suggestions
        
        Args:
            error: The exception to classify
            
        Returns:
            Dict with error classification and recovery info
        """
        error_message = str(error).lower()
        error_type = type(error).__name__
        
        # Check for known error patterns
        for error_key, error_info in self.error_patterns.items():
            for pattern in error_info['pattern']:
                if pattern in error_message:
                    return {
                        'error_key': error_key,
                        'error_type': error_info['error_type'].__name__,
                        'original_error': str(error),
                        'recovery_suggestions': error_info['recovery_suggestions'],
                        'severity': self._get_error_severity(error_key)
                    }
        
        # Unknown error
        return {
            'error_key': 'unknown',
            'error_type': error_type,
            'original_error': str(error),
            'recovery_suggestions': [
                "This is an unexpected error",
                "Check the logs for more details",
                "Try the operation again",
                "Contact support if the problem persists"
            ],
            'severity': 'high'
        }
    
    def _get_error_severity(self, error_key: str) -> str:
        """Get the severity level of an error"""
        high_severity = ['contract_not_found', 'insufficient_funds']
        medium_severity = ['timeout', 'nonce_too_low', 'gas_limit_exceeded']
        
        if error_key in high_severity:
            return 'high'
        elif error_key in medium_severity:
            return 'medium'
        else:
            return 'low'
    
    def handle_error(self, error: Exception, context: str = "") -> Dict[str, Any]:
        """Handle an error with classification and logging
        
        Args:
            error: The exception to handle
            context: Additional context about where the error occurred
            
        Returns:
            Dict with error information and recovery suggestions
        """
        error_info = self.classify_error(error)
        
        # Log the error with appropriate level
        log_message = f"Error in {context}: {error_info['original_error']}"
        
        if error_info['severity'] == 'high':
            logger.error(log_message)
        elif error_info['severity'] == 'medium':
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # Log recovery suggestions
        logger.info("💡 Recovery suggestions:")
        for i, suggestion in enumerate(error_info['recovery_suggestions'], 1):
            logger.info(f"   {i}. {suggestion}")
        
        return error_info

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0,
                    exceptions: tuple = (Exception,)):
    """Decorator to retry function calls on failure with exponential backoff
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry
        exceptions: Tuple of exceptions to catch and retry on
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        # Final attempt failed
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries")
                        error_handler.handle_error(e, f"{func.__name__} (final attempt)")
                        raise e
                    
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                    logger.info(f"Retrying in {current_delay:.1f} seconds...")
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator

def safe_blockchain_call(func: Callable) -> Callable:
    """Decorator for safe blockchain calls with comprehensive error handling"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_info = error_handler.handle_error(e, func.__name__)
            
            # Return a standardized error response
            return {
                'success': False,
                'error': error_info['original_error'],
                'error_type': error_info['error_type'],
                'error_code': error_info['error_key'],
                'recovery_suggestions': error_info['recovery_suggestions'],
                'severity': error_info['severity']
            }
    
    return wrapper

# Global error handler instance
error_handler = ErrorHandler()

# Common retry configurations
network_retry = retry_on_failure(
    max_retries=3, 
    delay=2.0, 
    exceptions=(ConnectionError, Timeout, RequestException, Web3Exception)
)

transaction_retry = retry_on_failure(
    max_retries=2, 
    delay=5.0, 
    exceptions=(TransactionNotFound, BlockNotFound)
)
