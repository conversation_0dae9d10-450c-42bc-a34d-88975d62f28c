<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Calendrier des Rendez-vous - CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .meeting-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 4px;
    }
    .meeting-scheduled { background-color: #3b82f6; }
    .meeting-completed { background-color: #10b981; }
    .meeting-cancelled { background-color: #ef4444; }
    .meeting-rescheduled { background-color: #f59e0b; }

    .calendar-day {
      min-height: 120px;
      transition: all 0.2s ease;
    }

    .calendar-day:hover {
      background-color: #f8fafc;
    }

    .meeting-item {
      font-size: 0.75rem;
      padding: 2px 4px;
      margin: 1px 0;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .meeting-item:hover {
      transform: scale(1.02);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .meeting-mentoring { background-color: #dbeafe; color: #1e40af; }
    .meeting-consultation { background-color: #dcfce7; color: #166534; }
    .meeting-review { background-color: #fef3c7; color: #92400e; }
    .meeting-project_help { background-color: #fce7f3; color: #be185d; }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
            CertifMentor
          </a>
        </div>

        <!-- Navigation -->
        <nav class="flex items-center space-x-6">
          <!-- Conversation Button -->
          <button id="conversationButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
            </svg>
            <span class="nav-button-text">Conversations</span>
          </button>

          <!-- Calendar Button (Active) -->
          <button id="calendrierButton" class="nav-button bg-blue-100 text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            <span class="nav-button-text">Calendrier</span>
          </button>

          <!-- Mentorship Button -->
          <button id="mentorshipButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="m22 2-5 10-5-4-5 10"></path>
            </svg>
            <span class="nav-button-text">Mentorat</span>
          </button>
        </nav>

        <!-- User Menu -->
        {% if user.is_authenticated %}
          <div class="relative">
            <button id="userMenuButton" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
              <span class="mr-2 text-lg">{{ user.first_name|default:user.username }}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
              <div class="py-2">
                <div class="user-menu-item" id="profileButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  <span>Mon Profil</span>
                </div>
                <div class="user-menu-item" id="logoutButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16,17 21,12 16,7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                  <span>Déconnexion</span>
                </div>
                <div class="user-menu-item" id="securityButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <circle cx="12" cy="16" r="1"></circle>
                    <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
                  </svg>
                  <span>Sécurité</span>
                </div>
                <div class="user-menu-item" id="becomeMentorButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="m22 2-5 10-5-4-5 10"></path>
                  </svg>
                  <span>Devenir mentor</span>
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <div class="flex items-center space-x-4">
            <a href="/connexion/" class="text-blue-600 hover:underline text-lg">Se connecter</a>
            <a href="/inscription/" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
          </div>
        {% endif %}
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Calendar Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-gray-900">Calendrier des Rendez-vous</h1>

          <!-- Month Navigation -->
          <div class="flex items-center space-x-4">
            <a href="?year={{ prev_year }}&month={{ prev_month }}"
               class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </a>

            <h2 class="text-xl font-semibold text-gray-800 min-w-[200px] text-center">
              {{ current_month }} {{ current_year }}
            </h2>

            <a href="?year={{ next_year }}&month={{ next_month }}"
               class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </a>
          </div>
        </div>

        <!-- Legend -->
        <div class="mt-4 flex flex-wrap items-center gap-4 text-sm">
          <div class="flex items-center">
            <span class="meeting-dot meeting-scheduled"></span>
            <span class="text-gray-600">Programmé</span>
          </div>
          <div class="flex items-center">
            <span class="meeting-dot meeting-completed"></span>
            <span class="text-gray-600">Terminé</span>
          </div>
          <div class="flex items-center">
            <span class="meeting-dot meeting-cancelled"></span>
            <span class="text-gray-600">Annulé</span>
          </div>
          <div class="flex items-center">
            <span class="meeting-dot meeting-rescheduled"></span>
            <span class="text-gray-600">Reprogrammé</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Days of Week Header -->
      <div class="grid grid-cols-7 border-b border-gray-200">
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Lun</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Mar</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Mer</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Jeu</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Ven</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Sam</div>
        <div class="p-4 text-center font-semibold text-gray-700 bg-gray-50">Dim</div>
      </div>

      <!-- Calendar Days -->
      {% for week in calendar_weeks %}
        <div class="grid grid-cols-7 border-b border-gray-200 last:border-b-0">
          {% for day in week %}
            <div class="calendar-day border-r border-gray-200 last:border-r-0 p-2 {% if day == 0 %}bg-gray-50{% endif %}">
              {% if day != 0 %}
                <!-- Day Number -->
                <div class="flex justify-between items-start mb-2">
                  <span class="text-sm font-medium text-gray-900
                    {% if day == today.day and current_date.month == today.month and current_date.year == today.year %}
                      bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center
                    {% endif %}">
                    {{ day }}
                  </span>
                </div>

                <!-- Meetings for this day -->
                {% for date, meetings in meetings_by_date.items %}
                  {% if date.day == day %}
                    {% for meeting in meetings %}
                      <div class="meeting-item meeting-{{ meeting.meeting_type }} mb-1"
                           onclick="showMeetingDetails('{{ meeting.id }}', '{{ meeting.title }}', '{{ meeting.mentor_name }}', '{{ meeting.start_time }}', '{{ meeting.end_time }}', '{{ meeting.meeting_type }}')">
                        <div class="flex items-center">
                          <span class="meeting-dot meeting-{{ meeting.status }}"></span>
                          <span class="truncate">{{ meeting.start_time }} - {{ meeting.mentor_name }}</span>
                        </div>
                        <div class="truncate font-medium">{{ meeting.title }}</div>
                      </div>
                    {% endfor %}
                  {% endif %}
                {% endfor %}
              {% endif %}
            </div>
          {% endfor %}
        </div>
      {% endfor %}
    </div>
  </main>

  <!-- Meeting Details Modal -->
  <div id="meetingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
            <button onclick="closeMeetingModal()" class="text-gray-400 hover:text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m18 6-12 12"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>

          <div class="space-y-3">
            <div>
              <span class="text-sm font-medium text-gray-500">Mentor:</span>
              <span id="modalMentor" class="ml-2 text-gray-900"></span>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-500">Horaire:</span>
              <span id="modalTime" class="ml-2 text-gray-900"></span>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-500">Type:</span>
              <span id="modalType" class="ml-2 text-gray-900"></span>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button onclick="closeMeetingModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              Fermer
            </button>
            <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Rejoindre
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .nav-button {
      @apply flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors;
    }

    .nav-button-icon {
      @apply w-5 h-5;
    }

    .nav-button-text {
      @apply text-sm font-medium;
    }

    .user-menu-item {
      @apply flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer transition-colors;
    }
  </style>

  <script>
    // Navigation functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Conversation Button
      const conversationButton = document.getElementById('conversationButton');
      if (conversationButton) {
        conversationButton.addEventListener('click', function() {
          window.location.href = '/conversations/';
        });
      }

      // Mentorship Button
      const mentorshipButton = document.getElementById('mentorshipButton');
      if (mentorshipButton) {
        mentorshipButton.addEventListener('click', function() {
          window.location.href = '/mentorship/';
        });
      }

      // User Menu
      const userMenuButton = document.getElementById('userMenuButton');
      const userMenu = document.getElementById('userMenu');

      if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
          e.stopPropagation();
          userMenu.classList.toggle('hidden');
        });

        document.addEventListener('click', function() {
          userMenu.classList.add('hidden');
        });

        userMenu.addEventListener('click', function(e) {
          e.stopPropagation();
        });

        // Profile functionality
        const profileButton = document.getElementById('profileButton');
        if (profileButton) {
          profileButton.addEventListener('click', function() {
            window.location.href = '/users/profile/';
          });
        }

        // Logout functionality
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
          logoutButton.addEventListener('click', function() {
            window.location.href = '/deconnexion/';
          });
        }

        // Security functionality
        const securityButton = document.getElementById('securityButton');
        if (securityButton) {
          securityButton.addEventListener('click', function() {
            console.log('Security clicked');
          });
        }

        // Become mentor functionality
        const becomeMentorButton = document.getElementById('becomeMentorButton');
        if (becomeMentorButton) {
          becomeMentorButton.addEventListener('click', function() {
            console.log('Become mentor clicked');
          });
        }
      }
    });

    // Meeting modal functionality
    function showMeetingDetails(id, title, mentor, startTime, endTime, type) {
      document.getElementById('modalTitle').textContent = title;
      document.getElementById('modalMentor').textContent = mentor;
      document.getElementById('modalTime').textContent = startTime + ' - ' + endTime;

      // Format meeting type
      const typeMap = {
        'mentoring': 'Mentorat',
        'consultation': 'Consultation',
        'review': 'Révision',
        'project_help': 'Aide projet'
      };
      document.getElementById('modalType').textContent = typeMap[type] || type;

      document.getElementById('meetingModal').classList.remove('hidden');
    }

    function closeMeetingModal() {
      document.getElementById('meetingModal').classList.add('hidden');
    }

    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeMeetingModal();
      }
    });
  </script>
</body>
</html>
