#!/usr/bin/env python3
"""
Quick balance check for SKALE Europa wallet
"""
from web3 import Web3
import sys

def check_balance():
    try:
        # Connect to SKALE Europa
        rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        if not w3.is_connected():
            print("❌ Failed to connect to SKALE Europa")
            return
        
        # Your wallet address
        wallet_address = "******************************************"
        
        # Get balance
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        
        print(f"🔍 WALLET BALANCE CHECK")
        print(f"=" * 40)
        print(f"Wallet: {wallet_address}")
        print(f"Network: SKALE Europa")
        print(f"Balance: {balance_ether} sFUEL")
        print(f"Balance (Wei): {balance_wei}")
        
        if balance_ether > 0:
            print(f"✅ You have sFUEL! Ready to deploy contracts.")
        else:
            print(f"❌ No sFUEL. Need to get some from faucet.")
            
    except Exception as e:
        print(f"❌ Error checking balance: {e}")

if __name__ == "__main__":
    check_balance()
