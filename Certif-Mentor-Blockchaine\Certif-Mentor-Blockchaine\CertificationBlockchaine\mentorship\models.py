from django.db import models
from django.contrib.auth.models import User

class Meeting(models.Model):
    """Model to represent scheduled meetings between mentors and students"""

    MEETING_STATUS_CHOICES = [
        ('scheduled', 'Programmé'),
        ('completed', 'Terminé'),
        ('cancelled', 'Annulé'),
        ('rescheduled', 'Reprogrammé'),
    ]

    MEETING_TYPE_CHOICES = [
        ('consultation', 'Consultation'),
        ('mentoring', 'Mentorat'),
        ('review', 'Révision'),
        ('project_help', 'Aide projet'),
    ]

    # Participants
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='student_meetings')
    mentor_id = models.IntegerField()  # Using mentor ID since mentors are hardcoded for now
    mentor_name = models.CharField(max_length=100)

    # Meeting details
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    meeting_type = models.Char<PERSON><PERSON>(max_length=20, choices=MEETING_TYPE_CHOICES, default='mentoring')

    # Scheduling
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    duration_minutes = models.IntegerField(default=60)

    # Status and metadata
    status = models.CharField(max_length=20, choices=MEETING_STATUS_CHOICES, default='scheduled')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Additional info
    meeting_link = models.URLField(blank=True, null=True)  # For video calls
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['date', 'start_time']

    def __str__(self):
        return f"{self.title} - {self.mentor_name} avec {self.student.username} le {self.date}"

    @property
    def is_upcoming(self):
        from django.utils import timezone
        from datetime import datetime, time

        now = timezone.now()
        meeting_datetime = timezone.make_aware(
            datetime.combine(self.date, self.start_time)
        )
        return meeting_datetime > now and self.status == 'scheduled'
