#!/usr/bin/env python3
"""
Simple test to verify certificate registration is working
"""
import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_registration_endpoint():
    """Test the certificate registration endpoint"""
    print("🧪 Testing Certificate Registration Endpoint")
    print("=" * 50)
    
    # Test data
    test_data = {
        'certificate_name': 'Test Blockchain Certificate',
        'issuer': 'Test University',
        'recipient': '<PERSON>',
        'certificate_number': 'TEST-2025-001',
        'description': 'This is a test certificate for blockchain registration'
    }
    
    # Create a test file content
    test_file_content = "This is a test certificate content for blockchain registration."
    
    try:
        # Test the Django view directly
        from blockchain.views import register_certificate
        from django.test import RequestFactory
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        factory = RequestFactory()
        
        # Create a test file
        test_file = SimpleUploadedFile(
            "test_certificate.txt",
            test_file_content.encode('utf-8'),
            content_type="text/plain"
        )
        
        # Create POST request
        request = factory.post('/register-certificate/', {
            **test_data,
            'certificate_file': test_file
        })
        
        # Call the view
        response = register_certificate(request)
        
        if response.status_code == 200:
            try:
                result = json.loads(response.content.decode('utf-8'))
                if result.get('success'):
                    print("✅ Certificate registration successful!")
                    print(f"   Certificate ID: {result.get('certificate_id', 'N/A')[:16]}...")
                    print(f"   Transaction Hash: {result.get('transaction_hash', 'N/A')[:16]}...")
                    print(f"   File Hash: {result.get('file_hash', 'N/A')[:16]}...")
                    if 'explorer_url' in result:
                        print(f"   Explorer URL: {result['explorer_url']}")
                    return True
                else:
                    print(f"❌ Registration failed: {result.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                print("✅ Registration page loaded successfully (HTML response)")
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_blockchain_service_directly():
    """Test the blockchain service directly"""
    print("\n🔗 Testing Blockchain Service Directly")
    print("=" * 40)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        # Test connection
        print("Testing blockchain connection...")
        balance = blockchain_service.get_account_balance()
        print(f"✅ Connected! Wallet Balance: {balance:.6f} sFUEL")
        
        # Test certificate registration
        test_data = {
            'name': 'Direct Test Certificate',
            'issuer': 'Direct Test University',
            'recipient': 'Jane Doe',
            'issue_date': '2025-06-28',
            'certificate_number': 'DIRECT-TEST-001',
            'description': 'Direct blockchain service test'
        }
        
        test_content = b"Direct test certificate content"
        
        print("Registering certificate on blockchain...")
        result = blockchain_service.register_certificate(test_data, test_content)
        
        if result['success']:
            print("✅ Direct blockchain registration successful!")
            print(f"   Certificate ID: {result['certificate_id'][:16]}...")
            print(f"   Transaction Hash: {result['transaction_hash'][:16]}...")
            return True
        else:
            print(f"❌ Direct registration failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Direct blockchain test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Certificate Registration Test Suite")
    print("=" * 60)
    
    # Test 1: Django endpoint
    endpoint_success = test_registration_endpoint()
    
    # Test 2: Direct blockchain service
    blockchain_success = test_blockchain_service_directly()
    
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"Django Endpoint: {'✅ PASS' if endpoint_success else '❌ FAIL'}")
    print(f"Blockchain Service: {'✅ PASS' if blockchain_success else '❌ FAIL'}")
    
    if endpoint_success and blockchain_success:
        print("\n🎉 All tests passed! Certificate registration is working!")
    elif endpoint_success:
        print("\n⚠️  Django endpoint works, but blockchain service has issues.")
    else:
        print("\n❌ Tests failed. Check the error messages above.")
