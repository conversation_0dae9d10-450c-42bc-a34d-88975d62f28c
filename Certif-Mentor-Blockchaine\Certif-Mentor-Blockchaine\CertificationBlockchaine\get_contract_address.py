#!/usr/bin/env python3
"""
Script to get or deploy contract address for certificate registration
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

from blockchain.blockchain_service import blockchain_service

def main():
    print("🔍 Checking contract status...")
    print("=" * 50)
    
    # Check if contract is already deployed
    if blockchain_service.contract_address:
        print(f"✅ Contract already deployed!")
        print(f"📍 Contract Address: {blockchain_service.contract_address}")
        print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{blockchain_service.contract_address}")
        return blockchain_service.contract_address
    
    print("⚠️  No contract address found. Deploying new contract...")
    
    # Check wallet balance
    try:
        balance = blockchain_service.get_account_balance()
        print(f"💰 Wallet balance: {balance:.6f} sFUEL")
        
        if balance < 0.001:
            print("❌ Insufficient balance for deployment!")
            return None
            
    except Exception as e:
        print(f"❌ Failed to check balance: {e}")
        return None
    
    # Deploy new contract
    try:
        print("🚀 Deploying new contract...")
        success = blockchain_service.deploy_new_contract()
        
        if success and blockchain_service.contract_address:
            print(f"✅ Contract deployed successfully!")
            print(f"📍 Contract Address: {blockchain_service.contract_address}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{blockchain_service.contract_address}")
            return blockchain_service.contract_address
        else:
            print("❌ Contract deployment failed!")
            return None
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return None

if __name__ == "__main__":
    contract_address = main()
    if contract_address:
        print(f"\n🎉 Contract ready for use: {contract_address}")
    else:
        print("\n❌ Contract deployment failed. Please check your wallet and try again.")
