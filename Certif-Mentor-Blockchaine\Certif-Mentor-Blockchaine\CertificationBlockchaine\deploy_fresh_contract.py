#!/usr/bin/env python3
"""
Deploy a fresh CertificateRegistry contract with your MetaMask wallet
Simple and reliable deployment script
"""
import os
import json
from web3 import Web3
from eth_account import Account

def deploy_fresh_contract():
    print("🚀 DEPLOYING FRESH CONTRACT WITH YOUR WALLET")
    print("=" * 60)
    
    # Your wallet configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    # Derive wallet address
    account = Account.from_key(private_key)
    wallet_address = account.address
    
    print(f"🔑 Your Wallet: {wallet_address}")
    print(f"🌐 Network: SKALE Europa DeFi Hub")
    print(f"🔗 Chain ID: {chain_id}")
    
    # Connect to SKALE Europa
    print("\n🌐 Connecting to SKALE Europa...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        print("🔧 Please check your internet connection")
        return False
    
    print("✅ Connected to SKALE Europa!")
    
    # Check balance
    try:
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        print(f"💰 Your Balance: {balance_ether:.6f} sFUEL")
        
        if balance_ether < 0.001:
            print("⚠️  Low balance, but SKALE has zero gas fees so deployment should work")
    except Exception as e:
        print(f"⚠️  Could not check balance: {e}")
    
    # Read contract source
    print("\n📝 Reading contract source...")
    contract_path = os.path.join(os.path.dirname(__file__), '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol')
    
    try:
        with open(contract_path, 'r', encoding='utf-8') as f:
            contract_source = f.read()
        print("✅ Contract source loaded")
    except Exception as e:
        print(f"❌ Failed to read contract: {e}")
        return False
    
    # Use pre-compiled bytecode (to avoid compilation issues)
    print("\n⚙️  Using pre-compiled contract...")
    
    # This is the compiled bytecode for CertificateRegistry.sol
    bytecode = "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"
    
    # Contract ABI (interface)
    abi = [
        {
            "inputs": [
                {"internalType": "string", "name": "_name", "type": "string"},
                {"internalType": "string", "name": "_issuer", "type": "string"},
                {"internalType": "string", "name": "_recipient", "type": "string"},
                {"internalType": "uint256", "name": "_issueDate", "type": "uint256"},
                {"internalType": "string", "name": "_certificateNumber", "type": "string"},
                {"internalType": "string", "name": "_description", "type": "string"},
                {"internalType": "string", "name": "_fileHash", "type": "string"}
            ],
            "name": "registerCertificate",
            "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
            "stateMutability": "nonpayable",
            "type": "function"
        },
        {
            "inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}],
            "name": "verifyCertificate",
            "outputs": [
                {"internalType": "string", "name": "name", "type": "string"},
                {"internalType": "string", "name": "issuer", "type": "string"},
                {"internalType": "string", "name": "recipient", "type": "string"},
                {"internalType": "uint256", "name": "issueDate", "type": "uint256"},
                {"internalType": "string", "name": "certificateNumber", "type": "string"},
                {"internalType": "string", "name": "description", "type": "string"},
                {"internalType": "string", "name": "fileHash", "type": "string"},
                {"internalType": "address", "name": "registeredBy", "type": "address"},
                {"internalType": "uint256", "name": "registrationDate", "type": "uint256"},
                {"internalType": "bool", "name": "exists", "type": "bool"}
            ],
            "stateMutability": "view",
            "type": "function"
        },
        {
            "inputs": [],
            "name": "getTotalCertificates",
            "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
            "stateMutability": "view",
            "type": "function"
        }
    ]
    
    # Deploy contract
    print("\n🚀 Deploying contract to blockchain...")
    try:
        # Create contract instance
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)
        
        # Get current gas price from network
        gas_price = w3.eth.gas_price
        print(f"🔍 Network gas price: {gas_price}")

        # Build transaction with higher gas limit
        transaction = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': 8000000,  # Increased gas limit for large contract
            'gasPrice': 0,  # SKALE Europa should have zero gas price
            'nonce': w3.eth.get_transaction_count(wallet_address),
        })
        
        print(f"⛽ Gas Limit: {transaction['gas']}")
        print(f"💰 Gas Price: {transaction['gasPrice']} (FREE on SKALE)")
        
        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)
        
        # Send transaction
        print("📤 Sending deployment transaction...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
        print(f"📋 Transaction Hash: {tx_hash.hex()}")
        
        # Wait for receipt
        print("⏳ Waiting for deployment confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if tx_receipt.status == 1:
            contract_address = tx_receipt.contractAddress
            print("\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print("=" * 60)
            print(f"📍 Contract Address: {contract_address}")
            print(f"⛽ Gas Used: {tx_receipt.gasUsed}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{contract_address}")
            print(f"🔍 Transaction: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
            
            # Save ABI
            abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
            with open(abi_path, 'w') as f:
                json.dump(abi, f, indent=2)
            print(f"💾 ABI saved to: {abi_path}")
            
            # Update blockchain service
            update_blockchain_service(contract_address)
            
            return contract_address
        else:
            print("❌ Deployment failed!")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

def update_blockchain_service(contract_address):
    """Update blockchain service with new contract address"""
    service_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'blockchain_service.py')
    
    try:
        # Read current file
        with open(service_path, 'r') as f:
            content = f.read()
        
        # Replace the contract address line
        old_line = 'self.contract_address = None  # Will be set after deployment'
        new_line = f'self.contract_address = "{contract_address}"  # Deployed contract address'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Write updated file
            with open(service_path, 'w') as f:
                f.write(content)
            
            print(f"✅ Blockchain service updated with contract address: {contract_address}")
        else:
            print("⚠️  Could not update blockchain service automatically")
            print(f"📝 Please manually update contract_address to: {contract_address}")
            
    except Exception as e:
        print(f"❌ Failed to update blockchain service: {e}")

if __name__ == "__main__":
    print("🧹 FRESH CONTRACT DEPLOYMENT")
    print("This will deploy a brand new contract with your wallet")
    print()
    
    contract_address = deploy_fresh_contract()
    if contract_address:
        print(f"\n🎯 SUCCESS! Your new contract is ready!")
        print(f"📋 Contract Address: {contract_address}")
        print(f"🌐 You can now register certificates on the blockchain!")
        print(f"📊 Current certificates in contract: 0 (brand new)")
    else:
        print("\n❌ Deployment failed. Please check the error messages above.")
