#!/usr/bin/env python3
"""
Get wallet information from private key
"""
from eth_account import Account

def get_wallet_info():
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    # Derive wallet address
    account = Account.from_key(private_key)
    wallet_address = account.address
    
    print("🔑 YOUR WALLET INFORMATION")
    print("=" * 40)
    print(f"Wallet Address: {wallet_address}")
    print(f"Private Key: {private_key}")
    print()
    print("🌐 SKALE EUROPA NETWORK INFO")
    print("=" * 40)
    print("Network Name: SKALE Europa DeFi Hub")
    print("RPC URL: https://mainnet.skalenodes.com/v1/elated-tan-skat")
    print("Chain ID: **********")
    print("Currency: sFUEL")
    print("Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/")
    print()
    print("📋 NEXT STEPS")
    print("=" * 40)
    print("1. Check if SKALE explorer loads in your browser")
    print("2. Make sure MetaMask is connected to SKALE Europa")
    print("3. Check if your wallet has sFUEL balance")

if __name__ == "__main__":
    get_wallet_info()
