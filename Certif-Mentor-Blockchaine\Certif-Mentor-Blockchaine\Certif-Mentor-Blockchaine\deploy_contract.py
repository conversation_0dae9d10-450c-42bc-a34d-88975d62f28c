#!/usr/bin/env python3
"""
Script to deploy the CertificateRegistry smart contract to SKALE Europa
"""
import os
import sys
import json
from solcx import compile_source, install_solc
from dotenv import load_dotenv

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from CertificationBlockchaine.blockchain.blockchain_service import blockchain_service

def compile_contract():
    """Compile the Solidity contract"""
    print("📝 Compiling smart contract...")
    
    # Install Solidity compiler
    try:
        install_solc('0.8.19')
    except Exception as e:
        print(f"Solidity compiler already installed or error: {e}")
    
    # Read contract source
    contract_path = os.path.join(os.path.dirname(__file__), 'CertificateRegistry.sol')
    with open(contract_path, 'r') as f:
        contract_source = f.read()
    
    # Compile contract
    compiled_sol = compile_source(
        contract_source,
        output_values=['abi', 'bin'],
        solc_version='0.8.19'
    )
    
    # Get contract interface
    contract_id, contract_interface = compiled_sol.popitem()
    
    return contract_interface['abi'], contract_interface['bin']

def save_contract_abi(abi):
    """Save contract ABI to file"""
    abi_path = os.path.join(
        os.path.dirname(__file__), 
        'CertificationBlockchaine', 
        'blockchain', 
        'contract_abi.json'
    )
    
    with open(abi_path, 'w') as f:
        json.dump(abi, f, indent=2)
    
    print(f"💾 Contract ABI saved to: {abi_path}")

def update_env_file(contract_address):
    """Update .env file with contract address"""
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    
    # Read current .env content
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    # Update CONTRACT_ADDRESS line
    updated_lines = []
    for line in lines:
        if line.startswith('CONTRACT_ADDRESS='):
            updated_lines.append(f'CONTRACT_ADDRESS={contract_address}\n')
        else:
            updated_lines.append(line)
    
    # Write back to .env
    with open(env_path, 'w') as f:
        f.writelines(updated_lines)
    
    print(f"🔧 Updated .env file with contract address: {contract_address}")

def main():
    """Main deployment function"""
    print("🚀 Starting CertificateRegistry deployment to SKALE Europa...")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Check connection
    print(f"🌐 Connecting to SKALE Europa DeFi Hub...")
    print(f"   RPC URL: https://mainnet.skalenodes.com/v1/elated-tan-skat")
    print(f"   Chain ID: **********")
    print(f"   Wallet: ******************************************")
    
    # Check balance
    balance = blockchain_service.get_account_balance()
    print(f"   Balance: {balance:.6f} sFUEL")
    
    if balance < 0.001:
        print("⚠️  Warning: Low balance. Make sure you have enough sFUEL for deployment.")
    
    # Compile contract
    try:
        abi, bytecode = compile_contract()
        print("✅ Contract compiled successfully!")
    except Exception as e:
        print(f"❌ Failed to compile contract: {e}")
        return False
    
    # Save ABI
    save_contract_abi(abi)
    
    # Deploy contract
    print("\n🚀 Deploying contract to SKALE Europa...")
    deployment_result = blockchain_service.deploy_contract(bytecode, abi)
    
    if deployment_result['success']:
        contract_address = deployment_result['contract_address']
        tx_hash = deployment_result['transaction_hash']
        gas_used = deployment_result['gas_used']
        
        print("✅ Contract deployed successfully!")
        print(f"   Contract Address: {contract_address}")
        print(f"   Transaction Hash: {tx_hash}")
        print(f"   Gas Used: {gas_used}")
        print(f"   Block Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash}")
        
        # Update .env file
        update_env_file(contract_address)
        
        print("\n🎉 Deployment completed successfully!")
        print("=" * 60)
        print("Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run Django migrations: python manage.py migrate")
        print("3. Start the Django server: python manage.py runserver")
        print("4. Test certificate registration on your website")
        
        return True
    else:
        print(f"❌ Deployment failed: {deployment_result['error']}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
