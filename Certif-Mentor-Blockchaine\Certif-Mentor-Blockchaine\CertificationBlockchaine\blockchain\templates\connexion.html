<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Connexion</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
  <div class="bg-white p-8 rounded-2xl shadow-md w-full max-w-md">
    <h1 class="text-2xl font-bold text-center text-gray-800 mb-6">Se connecter</h1>

    {% if messages %}
      {% for message in messages %}
        <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-3 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <span class="text-red-700 text-sm font-medium">{{ message }}</span>
        </div>
      {% endfor %}
    {% endif %}

    <form method="post" class="space-y-4">
      {% csrf_token %}

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700">Adresse e-mail</label>
        <input type="text" id="email" name="email" value="{{ form_data.email }}" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <div>
        <label for="mot_de_passe" class="block text-sm font-medium text-gray-700">Mot de passe</label>
        <input type="password" id="mot_de_passe" name="mot_de_passe" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <button type="submit" class="w-full bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition">
        Se connecter
      </button>
    </form>

    <div class="text-center mt-6">
      <p class="text-sm text-gray-600">
        Vous n'avez pas de compte ?
      </p>
      <a href="{% url 'inscription' %}" class="text-blue-600 hover:text-blue-700 font-medium hover:underline transition-colors">
        Créer un compte
      </a>
    </div>
  </div>
</body>
</html>
