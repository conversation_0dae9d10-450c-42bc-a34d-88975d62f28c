#!/usr/bin/env python3
"""
Check SKALE Europa contract size and gas limits
"""
from web3 import Web3

def check_skale_limits():
    print("🔍 CHECKING SKALE EUROPA LIMITS")
    print("=" * 50)
    
    # Connect to SKALE Europa
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return
    
    print("✅ Connected to SKALE Europa")
    
    # Get latest block info
    latest_block = w3.eth.get_block('latest')
    
    print(f"\n📊 BLOCK INFORMATION:")
    print(f"   Block Number: {latest_block['number']:,}")
    print(f"   Block Gas Limit: {latest_block['gasLimit']:,}")
    print(f"   Block Gas Used: {latest_block['gasUsed']:,}")
    print(f"   Available Gas: {latest_block['gasLimit'] - latest_block['gasUsed']:,}")
    
    # Check network info
    chain_id = w3.eth.chain_id
    gas_price = w3.eth.gas_price
    
    print(f"\n🌐 NETWORK INFORMATION:")
    print(f"   Chain ID: {chain_id}")
    print(f"   Gas Price: {gas_price}")
    print(f"   Network: {'SKALE Europa' if chain_id == ********** else 'UNKNOWN'}")
    
    # Analyze contract size limits
    print(f"\n📏 CONTRACT SIZE ANALYSIS:")
    
    # Typical Ethereum limits for reference
    ethereum_block_gas_limit = 30000000  # ~30M gas on Ethereum
    ethereum_contract_size_limit = 24576  # 24KB bytecode limit
    
    print(f"   SKALE Block Gas Limit: {latest_block['gasLimit']:,}")
    print(f"   Ethereum Block Gas Limit: {ethereum_block_gas_limit:,}")
    print(f"   SKALE vs Ethereum: {latest_block['gasLimit'] / ethereum_block_gas_limit:.2f}x")
    
    # Check our contract bytecode sizes
    print(f"\n🔍 OUR CONTRACT BYTECODE ANALYSIS:")
    
    # The NFT bytecode we were using (wrong one)
    nft_bytecode = "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"
    
    # Simple certificate bytecode we're trying to use
    simple_bytecode = "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"
    
    nft_size_bytes = len(nft_bytecode) // 2  # Divide by 2 because hex
    simple_size_bytes = len(simple_bytecode) // 2
    
    print(f"   NFT Contract Bytecode: {len(nft_bytecode):,} chars = {nft_size_bytes:,} bytes")
    print(f"   Simple Contract Bytecode: {len(simple_bytecode):,} chars = {simple_size_bytes:,} bytes")
    print(f"   Ethereum Size Limit: {ethereum_contract_size_limit:,} bytes")
    
    print(f"\n✅ SIZE CHECK RESULTS:")
    if nft_size_bytes > ethereum_contract_size_limit:
        print(f"   ❌ NFT Contract TOO LARGE: {nft_size_bytes:,} > {ethereum_contract_size_limit:,} bytes")
    else:
        print(f"   ✅ NFT Contract size OK: {nft_size_bytes:,} <= {ethereum_contract_size_limit:,} bytes")
        
    if simple_size_bytes > ethereum_contract_size_limit:
        print(f"   ❌ Simple Contract TOO LARGE: {simple_size_bytes:,} > {ethereum_contract_size_limit:,} bytes")
    else:
        print(f"   ✅ Simple Contract size OK: {simple_size_bytes:,} <= {ethereum_contract_size_limit:,} bytes")
    
    # Gas estimation
    print(f"\n⛽ GAS ESTIMATION:")
    print(f"   NFT Contract estimated gas: ~{nft_size_bytes * 200:,} (200 gas per byte)")
    print(f"   Simple Contract estimated gas: ~{simple_size_bytes * 200:,} (200 gas per byte)")
    print(f"   SKALE Block Gas Limit: {latest_block['gasLimit']:,}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if nft_size_bytes * 200 > latest_block['gasLimit']:
        print(f"   ❌ NFT Contract likely TOO COMPLEX for SKALE")
    else:
        print(f"   ✅ NFT Contract should fit in SKALE block")
        
    if simple_size_bytes * 200 > latest_block['gasLimit']:
        print(f"   ❌ Simple Contract likely TOO COMPLEX for SKALE")
    else:
        print(f"   ✅ Simple Contract should fit in SKALE block")

if __name__ == "__main__":
    check_skale_limits()
