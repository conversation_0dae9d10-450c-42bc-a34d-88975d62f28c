<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification de Certificat | CertifMentor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm py-4 px-8">
        <div class="max-w-6xl mx-auto flex justify-between items-center">
            <div class="flex items-center gap-4">
                <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700">CertifMentor</a>
                <span class="text-gray-400">|</span>
                <h1 class="text-xl font-semibold text-gray-800">Vérification de Certificat</h1>
            </div>
            <a href="/" class="text-gray-600 hover:text-gray-800">← Retour à l'accueil</a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 py-8">
        <!-- Introduction -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                        <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                        <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Vérification de Certificat Blockchain</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Vérifiez l'authenticité d'un certificat enregistré sur la blockchain SKALE Europa. 
                    Cette vérification est publique et accessible à tous.
                </p>
            </div>

            <!-- Verification Methods -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Method 1: Certificate ID -->
                <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors cursor-pointer" onclick="selectMethod('certificate_id')">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10,9 9,9 8,9"></polyline>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">ID de Certificat</h3>
                    </div>
                    <p class="text-gray-600 text-sm">Entrez l'ID unique du certificat pour vérifier son authenticité</p>
                </div>

                <!-- Method 2: File Upload -->
                <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors cursor-pointer" onclick="selectMethod('file_hash')">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">Fichier Original</h3>
                    </div>
                    <p class="text-gray-600 text-sm">Téléchargez le fichier original pour vérifier qu'il n'a pas été modifié</p>
                </div>
            </div>

            <!-- Verification Form -->
            <form id="verificationForm" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" id="verification_type" name="verification_type" value="">

                <!-- Certificate ID Method -->
                <div id="method_certificate_id" class="hidden">
                    <label for="certificate_id" class="block text-sm font-medium text-gray-700 mb-2">
                        ID du Certificat *
                    </label>
                    <input 
                        type="text" 
                        id="certificate_id" 
                        name="certificate_id" 
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Entrez l'ID du certificat (ex: a1b2c3d4e5f6...)"
                    >
                    <p class="text-sm text-gray-500 mt-1">L'ID du certificat est un identifiant unique de 64 caractères</p>
                </div>

                <!-- File Upload Method -->
                <div id="method_file_hash" class="hidden">
                    <label for="certificate_file" class="block text-sm font-medium text-gray-700 mb-2">
                        Fichier du Certificat *
                    </label>
                    <input 
                        type="file" 
                        id="certificate_file" 
                        name="certificate_file" 
                        accept=".pdf,.jpg,.jpeg,.png"
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <p class="text-sm text-gray-500 mt-1">Téléchargez le fichier original du certificat</p>
                </div>

                <button 
                    type="submit" 
                    id="verifyButton"
                    class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:bg-gray-400"
                    disabled
                >
                    Vérifier le Certificat
                </button>
            </form>

            <!-- Results -->
            <div id="verificationResults" class="mt-8 hidden">
                <!-- Results will be displayed here -->
            </div>
        </div>

        <!-- How it Works -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Comment fonctionne la vérification ?</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-blue-600 font-bold text-lg">1</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">Blockchain SKALE Europa</h4>
                    <p class="text-gray-600 text-sm">Les certificats sont stockés de manière permanente et immuable sur la blockchain</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-green-600 font-bold text-lg">2</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">Vérification Cryptographique</h4>
                    <p class="text-gray-600 text-sm">Chaque certificat a un hash unique qui garantit son intégrité</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-purple-600 font-bold text-lg">3</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">Accès Public</h4>
                    <p class="text-gray-600 text-sm">Toute personne peut vérifier l'authenticité sans autorisation spéciale</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedMethod = '';

        function selectMethod(method) {
            selectedMethod = method;
            document.getElementById('verification_type').value = method;
            
            // Hide all method forms
            document.getElementById('method_certificate_id').classList.add('hidden');
            document.getElementById('method_file_hash').classList.add('hidden');
            
            // Show selected method form
            document.getElementById('method_' + method).classList.remove('hidden');
            
            // Enable verify button
            document.getElementById('verifyButton').disabled = false;
            
            // Update visual selection
            document.querySelectorAll('.border-gray-200').forEach(el => {
                el.classList.remove('border-blue-500', 'bg-blue-50');
                el.classList.add('border-gray-200');
            });
            
            event.target.closest('.border').classList.remove('border-gray-200');
            event.target.closest('.border').classList.add('border-blue-500', 'bg-blue-50');
        }

        document.getElementById('verificationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const button = document.getElementById('verifyButton');
            const results = document.getElementById('verificationResults');
            
            button.disabled = true;
            button.textContent = 'Vérification en cours...';
            
            const formData = new FormData(this);
            
            fetch('/verify-certificate/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                displayResults(data);
                button.disabled = false;
                button.textContent = 'Vérifier le Certificat';
            })
            .catch(error => {
                displayError('Erreur de connexion: ' + error);
                button.disabled = false;
                button.textContent = 'Vérifier le Certificat';
            });
        });

        function displayResults(data) {
            const results = document.getElementById('verificationResults');
            
            if (data.success && data.verified) {
                results.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                    <path d="M9 12l2 2 4-4"></path>
                                    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-green-800">✅ Certificat Vérifié et Authentique</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div><strong>Nom:</strong> ${data.certificate.name}</div>
                            <div><strong>Émetteur:</strong> ${data.certificate.issuer}</div>
                            <div><strong>Bénéficiaire:</strong> ${data.certificate.recipient}</div>
                            <div><strong>Date d'émission:</strong> ${data.certificate.issue_date}</div>
                            <div><strong>Date d'enregistrement:</strong> ${data.certificate.registration_date}</div>
                            <div><strong>Réseau blockchain:</strong> ${data.certificate.blockchain_network}</div>
                        </div>
                        <div class="mt-4 p-3 bg-white rounded border">
                            <strong>Hash de transaction:</strong> 
                            <code class="text-xs break-all">${data.certificate.transaction_hash}</code>
                        </div>
                    </div>
                `;
            } else if (data.success && !data.verified) {
                results.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-600">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="15" y1="9" x2="9" y2="15"></line>
                                    <line x1="9" y1="9" x2="15" y2="15"></line>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-red-800">❌ Certificat Non Trouvé</h3>
                        </div>
                        <p class="text-red-700">${data.message}</p>
                        <p class="text-sm text-red-600 mt-2">Ce certificat n'existe pas dans la blockchain ou l'ID/fichier est incorrect.</p>
                    </div>
                `;
            } else {
                displayError(data.error);
            }
            
            results.classList.remove('hidden');
        }

        function displayError(error) {
            const results = document.getElementById('verificationResults');
            results.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-red-800 mb-2">Erreur de Vérification</h3>
                    <p class="text-red-700">${error}</p>
                </div>
            `;
            results.classList.remove('hidden');
        }

        // Check for URL parameters (from QR code scan)
        window.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const certificateId = urlParams.get('id');

            if (certificateId) {
                // Auto-select certificate ID method and fill the field
                selectMethod('certificate_id');
                document.getElementById('certificate_id').value = certificateId;

                // Auto-submit the form after a short delay
                setTimeout(() => {
                    document.getElementById('verificationForm').dispatchEvent(new Event('submit'));
                }, 1000);
            }
        });
    </script>
</body>
</html>
