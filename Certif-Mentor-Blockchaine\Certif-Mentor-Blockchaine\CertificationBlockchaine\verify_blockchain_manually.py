#!/usr/bin/env python3
"""
Manual blockchain verification script
Use this to manually check if certificates are registered on SKALE Europa blockchain
"""
import os
import sys
import django
from web3 import Web3
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def connect_to_blockchain():
    """Connect to SKALE Europa blockchain"""
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if w3.is_connected():
        print(f"✅ Connected to SKALE Europa")
        print(f"   Latest Block: {w3.eth.block_number:,}")
        print(f"   Chain ID: {w3.eth.chain_id}")
        return w3
    else:
        print("❌ Failed to connect to blockchain")
        return None

def check_transaction(w3, tx_hash):
    """Check a specific transaction on the blockchain"""
    print(f"\n🔍 Checking Transaction: {tx_hash}")
    print("=" * 60)
    
    try:
        # Get transaction details
        tx = w3.eth.get_transaction(tx_hash)
        print(f"✅ Transaction found!")
        print(f"   From: {tx['from']}")
        print(f"   To: {tx['to']}")
        print(f"   Block Number: {tx['blockNumber']:,}")
        print(f"   Gas Used: {tx['gas']:,}")
        print(f"   Gas Price: {tx['gasPrice']} (should be 0 on SKALE)")
        
        # Get transaction receipt
        receipt = w3.eth.get_transaction_receipt(tx_hash)
        print(f"   Status: {'✅ Success' if receipt['status'] == 1 else '❌ Failed'}")
        print(f"   Actual Gas Used: {receipt['gasUsed']:,}")
        
        # Check if it's a contract interaction
        if tx['to']:
            print(f"   Contract Address: {tx['to']}")
            
            # Try to decode input data
            if tx['input'] and tx['input'] != '0x':
                print(f"   Input Data Length: {len(tx['input'])} characters")
                print(f"   Input Data (first 100 chars): {tx['input'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Transaction not found or error: {e}")
        return False

def check_contract_state(w3, contract_address):
    """Check the state of the certificate contract"""
    print(f"\n📋 Checking Contract State: {contract_address}")
    print("=" * 60)
    
    try:
        # Load contract ABI
        abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
        if os.path.exists(abi_path):
            with open(abi_path, 'r') as f:
                contract_abi = json.load(f)
            
            # Create contract instance
            contract = w3.eth.contract(address=contract_address, abi=contract_abi)
            
            # Get total certificates
            try:
                total_certs = contract.functions.getTotalCertificates().call()
                print(f"✅ Contract is active!")
                print(f"   Total Certificates: {total_certs}")
                
                # Get some recent certificates
                if total_certs > 0:
                    print(f"\n📜 Recent Certificates:")
                    start_idx = max(0, total_certs - 5)  # Last 5 certificates
                    for i in range(start_idx, total_certs):
                        try:
                            cert = contract.functions.getCertificate(i).call()
                            print(f"   Certificate {i}:")
                            print(f"     Name: {cert[0]}")
                            print(f"     Issuer: {cert[1]}")
                            print(f"     Recipient: {cert[2]}")
                            print(f"     Issue Date: {cert[3]}")
                            print(f"     Certificate Number: {cert[4]}")
                            print(f"     File Hash: {cert[6][:16]}...")
                        except Exception as e:
                            print(f"     Error reading certificate {i}: {e}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error calling contract functions: {e}")
                return False
                
        else:
            print(f"❌ Contract ABI not found at: {abi_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking contract: {e}")
        return False

def verify_certificate_by_id(w3, contract_address, certificate_id):
    """Verify a specific certificate by its ID"""
    print(f"\n🔍 Verifying Certificate ID: {certificate_id[:16]}...")
    print("=" * 60)
    
    try:
        # Load contract ABI
        abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
        if os.path.exists(abi_path):
            with open(abi_path, 'r') as f:
                contract_abi = json.load(f)
            
            contract = w3.eth.contract(address=contract_address, abi=contract_abi)
            
            # Check if certificate exists
            try:
                exists = contract.functions.certificateExists(certificate_id).call()
                if exists:
                    print(f"✅ Certificate found on blockchain!")
                    
                    # Get certificate details
                    cert_data = contract.functions.getCertificateById(certificate_id).call()
                    print(f"   Name: {cert_data[0]}")
                    print(f"   Issuer: {cert_data[1]}")
                    print(f"   Recipient: {cert_data[2]}")
                    print(f"   Issue Date: {cert_data[3]}")
                    print(f"   Certificate Number: {cert_data[4]}")
                    print(f"   Description: {cert_data[5]}")
                    print(f"   File Hash: {cert_data[6]}")
                    
                    return True
                else:
                    print(f"❌ Certificate not found on blockchain")
                    return False
                    
            except Exception as e:
                print(f"❌ Error verifying certificate: {e}")
                return False
                
        else:
            print(f"❌ Contract ABI not found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🔍 SKALE Europa Blockchain Manual Verification Tool")
    print("=" * 70)
    
    # Connect to blockchain
    w3 = connect_to_blockchain()
    if not w3:
        return
    
    # Get contract address
    try:
        from blockchain.blockchain_service import blockchain_service
        contract_address = blockchain_service.contract_address
        print(f"\n📋 Contract Address: {contract_address}")
    except:
        contract_address = "******************************************"  # Default
        print(f"\n📋 Using Default Contract Address: {contract_address}")
    
    while True:
        print("\n" + "=" * 70)
        print("Choose verification method:")
        print("1. Check transaction hash")
        print("2. Check contract state")
        print("3. Verify certificate by ID")
        print("4. Check wallet balance")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            tx_hash = input("Enter transaction hash: ").strip()
            if tx_hash:
                check_transaction(w3, tx_hash)
        
        elif choice == '2':
            check_contract_state(w3, contract_address)
        
        elif choice == '3':
            cert_id = input("Enter certificate ID: ").strip()
            if cert_id:
                verify_certificate_by_id(w3, contract_address, cert_id)
        
        elif choice == '4':
            wallet_address = "******************************************"
            balance_wei = w3.eth.get_balance(wallet_address)
            balance_ether = w3.from_wei(balance_wei, 'ether')
            print(f"\n💰 Wallet Balance: {balance_ether:.6f} sFUEL")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
