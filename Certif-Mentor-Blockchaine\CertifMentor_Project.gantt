@startgantt
!theme plain
title Certif-Mentor Blockchain Project Timeline

Project starts 2024-01-01

-- Planning Phase --
[Project Planning] starts 2024-01-01 and lasts 15 days
[Requirements Analysis] starts 2024-01-16 and lasts 15 days
[Technology Research] starts 2024-01-31 and lasts 15 days
[Architecture Design] starts 2024-02-15 and lasts 15 days

-- Backend Development --
[Django Project Setup] starts 2024-03-01 and lasts 7 days
[Database Design] starts 2024-03-08 and lasts 7 days
[User Authentication System] starts 2024-03-15 and lasts 14 days
[User Registration System] starts 2024-03-29 and lasts 14 days
[User Profile Management] starts 2024-04-12 and lasts 14 days
[Mentor Profile System] starts 2024-04-26 and lasts 14 days
[Course Management System] starts 2024-05-10 and lasts 14 days
[Mentorship Booking System] starts 2024-05-24 and lasts 14 days

-- Frontend Development --
[Frontend Development Setup] starts 2024-03-01 and lasts 7 days
[Tailwind CSS Integration] starts 2024-03-08 and lasts 7 days
[Home Page Interface] starts 2024-03-15 and lasts 14 days
[Search and Filter Interface] starts 2024-03-29 and lasts 14 days
[Mentor Search Interface] starts 2024-04-12 and lasts 14 days
[Course Search Interface] starts 2024-04-26 and lasts 14 days
[Calendar Integration] starts 2024-05-10 and lasts 14 days
[Booking Interface] starts 2024-05-24 and lasts 14 days

-- Blockchain Development --
[Blockchain Research] starts 2024-03-01 and lasts 14 days
[SKALE Network Setup] starts 2024-03-15 and lasts 7 days
[Smart Contract Development] starts 2024-03-22 and lasts 15 days
[Smart Contract Testing] starts 2024-04-06 and lasts 7 days

-- Certificate System --
[Certificate Template Design] starts 2024-06-07 and lasts 14 days
[Certificate Generation UI] starts 2024-06-21 and lasts 14 days
[Certificate Upload Interface] starts 2024-07-05 and lasts 14 days
[Certificate Verification Interface] starts 2024-07-19 and lasts 14 days

-- Integration Phase --
[Blockchain Service Implementation] starts 2024-08-02 and lasts 14 days
[Certificate Registration Backend] starts 2024-08-16 and lasts 14 days
[Web3 Integration] starts 2024-08-30 and lasts 14 days
[Wallet Connection Setup] starts 2024-09-13 and lasts 14 days

-- Current Debugging Phase --
[Contract Deployment Issues] starts 2024-09-27 and lasts 80 days
[Private Key Configuration] starts 2024-12-16 and lasts 7 days
[Python Environment Setup] starts 2024-12-23 and lasts 4 days

-- Final Phase --
[Final Contract Deployment] starts 2024-12-27 and lasts 4 days
[Certificate Registration Testing] starts 2024-12-31 and lasts 4 days
[End-to-End Testing] starts 2025-01-04 and lasts 7 days
[Bug Fixes and Optimization] starts 2025-01-11 and lasts 7 days
[User Acceptance Testing] starts 2025-01-18 and lasts 7 days
[Performance Testing] starts 2025-01-25 and lasts 7 days
[Security Audit] starts 2025-02-01 and lasts 7 days
[Final Documentation] starts 2025-02-08 and lasts 7 days

-- Deployment --
[Production Deployment] starts 2025-02-15 and lasts 7 days
[Go-Live and Monitoring] starts 2025-02-22 and lasts 7 days
[Project Closure] starts 2025-03-01 and lasts 7 days
[Final Presentation] starts 2025-03-08 and lasts 7 days

-- Dependencies --
[Requirements Analysis] starts after [Project Planning]'s end
[Technology Research] starts after [Requirements Analysis]'s end
[Architecture Design] starts after [Technology Research]'s end

[Django Project Setup] starts after [Architecture Design]'s end
[Database Design] starts after [Django Project Setup]'s end
[User Authentication System] starts after [Database Design]'s end
[User Registration System] starts after [User Authentication System]'s end
[User Profile Management] starts after [User Registration System]'s end
[Mentor Profile System] starts after [User Profile Management]'s end
[Course Management System] starts after [Mentor Profile System]'s end
[Mentorship Booking System] starts after [Course Management System]'s end

[Frontend Development Setup] starts after [Architecture Design]'s end
[Tailwind CSS Integration] starts after [Frontend Development Setup]'s end
[Home Page Interface] starts after [Tailwind CSS Integration]'s end
[Search and Filter Interface] starts after [Home Page Interface]'s end
[Mentor Search Interface] starts after [Search and Filter Interface]'s end
[Course Search Interface] starts after [Mentor Search Interface]'s end
[Calendar Integration] starts after [Course Search Interface]'s end
[Booking Interface] starts after [Calendar Integration]'s end

[Blockchain Research] starts after [Architecture Design]'s end
[SKALE Network Setup] starts after [Blockchain Research]'s end
[Smart Contract Development] starts after [SKALE Network Setup]'s end
[Smart Contract Testing] starts after [Smart Contract Development]'s end

[Certificate Template Design] starts after [Booking Interface]'s end
[Certificate Generation UI] starts after [Certificate Template Design]'s end
[Certificate Upload Interface] starts after [Certificate Generation UI]'s end
[Certificate Verification Interface] starts after [Certificate Upload Interface]'s end

[Blockchain Service Implementation] starts after [Smart Contract Testing]'s end
[Certificate Registration Backend] starts after [Blockchain Service Implementation]'s end
[Web3 Integration] starts after [Certificate Registration Backend]'s end
[Wallet Connection Setup] starts after [Web3 Integration]'s end

[Contract Deployment Issues] starts after [Wallet Connection Setup]'s end
[Private Key Configuration] starts after [Contract Deployment Issues]'s end
[Python Environment Setup] starts after [Private Key Configuration]'s end

[Final Contract Deployment] starts after [Python Environment Setup]'s end
[Certificate Registration Testing] starts after [Final Contract Deployment]'s end
[End-to-End Testing] starts after [Certificate Registration Testing]'s end
[Bug Fixes and Optimization] starts after [End-to-End Testing]'s end
[User Acceptance Testing] starts after [Bug Fixes and Optimization]'s end
[Performance Testing] starts after [User Acceptance Testing]'s end
[Security Audit] starts after [Performance Testing]'s end
[Final Documentation] starts after [Security Audit]'s end

[Production Deployment] starts after [Final Documentation]'s end
[Go-Live and Monitoring] starts after [Production Deployment]'s end
[Project Closure] starts after [Go-Live and Monitoring]'s end
[Final Presentation] starts after [Project Closure]'s end

@endgantt
