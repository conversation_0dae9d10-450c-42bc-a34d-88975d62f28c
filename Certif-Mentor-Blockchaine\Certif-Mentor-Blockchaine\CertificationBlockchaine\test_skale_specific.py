#!/usr/bin/env python3
"""
Test SKALE Europa specific blockchain requirements that could cause deployment failures
"""
import os
import sys
import django
from web3 import Web3
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_skale_network_specifics():
    """Test SKALE Europa specific network requirements"""
    print("🔍 SKALE Europa Network Specifics")
    print("=" * 50)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # Test 1: Chain ID verification
        chain_id = w3.eth.chain_id
        expected_chain_id = **********
        print(f"Chain ID: {chain_id}")
        if chain_id != expected_chain_id:
            print(f"❌ Wrong chain ID. Expected: {expected_chain_id}")
            return False
        else:
            print(f"✅ Correct SKALE Europa chain ID")
        
        # Test 2: Gas price behavior
        gas_price = w3.eth.gas_price
        print(f"Network gas price: {gas_price}")
        if gas_price == 0:
            print(f"✅ Zero gas price (true SKALE behavior)")
        else:
            print(f"⚠️  Non-zero gas price: {gas_price} (might need special handling)")
        
        # Test 3: Block gas limit
        latest_block = w3.eth.get_block('latest')
        gas_limit = latest_block['gasLimit']
        print(f"Block gas limit: {gas_limit:,}")
        
        if gas_limit < 8000000:
            print(f"❌ Low gas limit for contract deployment")
            return False
        else:
            print(f"✅ Sufficient gas limit for deployment")
        
        # Test 4: Check if SKALE has special transaction requirements
        print(f"\nBlock details:")
        print(f"   Block number: {latest_block['number']:,}")
        print(f"   Block size: {latest_block['size']:,} bytes")
        print(f"   Gas used: {latest_block['gasUsed']:,}")
        print(f"   Transactions: {len(latest_block['transactions'])}")
        
        # Test 5: Check wallet nonce and balance
        wallet_address = "******************************************"
        nonce = w3.eth.get_transaction_count(wallet_address)
        balance = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance, 'ether')
        
        print(f"\nWallet status:")
        print(f"   Address: {wallet_address}")
        print(f"   Nonce: {nonce}")
        print(f"   Balance: {balance_ether:.6f} sFUEL")
        
        if balance == 0:
            print(f"❌ Zero balance - cannot deploy")
            return False
        else:
            print(f"✅ Sufficient balance for deployment")
        
        return True
        
    except Exception as e:
        print(f"❌ SKALE network test failed: {e}")
        return False

def test_skale_transaction_format():
    """Test if SKALE requires special transaction formatting"""
    print("\n🔍 SKALE Transaction Format Requirements")
    print("=" * 50)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        # Test transaction building
        print("Testing transaction format...")
        
        # Get current nonce
        nonce = blockchain_service.w3.eth.get_transaction_count(blockchain_service.wallet_address)
        print(f"Current nonce: {nonce}")
        
        # Test gas estimation for a simple transaction
        try:
            # Try to estimate gas for a simple transfer
            gas_estimate = blockchain_service.w3.eth.estimate_gas({
                'from': blockchain_service.wallet_address,
                'to': blockchain_service.wallet_address,  # Self-transfer
                'value': 0,
                'gasPrice': 0
            })
            print(f"✅ Gas estimation works: {gas_estimate:,}")
        except Exception as e:
            print(f"❌ Gas estimation failed: {e}")
            
            # Try with network gas price
            try:
                gas_estimate = blockchain_service.w3.eth.estimate_gas({
                    'from': blockchain_service.wallet_address,
                    'to': blockchain_service.wallet_address,
                    'value': 0,
                    'gasPrice': blockchain_service.w3.eth.gas_price
                })
                print(f"✅ Gas estimation works with network gas price: {gas_estimate:,}")
            except Exception as e2:
                print(f"❌ Gas estimation still failed: {e2}")
                return False
        
        # Test if SKALE accepts EIP-1559 transactions
        try:
            latest_block = blockchain_service.w3.eth.get_block('latest')
            if 'baseFeePerGas' in latest_block:
                print(f"✅ SKALE supports EIP-1559 (base fee: {latest_block['baseFeePerGas']})")
            else:
                print(f"⚠️  SKALE uses legacy transaction format")
        except Exception as e:
            print(f"⚠️  Could not determine transaction format support: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transaction format test failed: {e}")
        return False

def test_contract_deployment_specifics():
    """Test SKALE-specific contract deployment requirements"""
    print("\n🔍 SKALE Contract Deployment Specifics")
    print("=" * 50)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        # Check if contract bytecode can be loaded
        try:
            with open('blockchain/contract_abi.json', 'r') as f:
                contract_abi = json.load(f)
            print(f"✅ Contract ABI loaded: {len(contract_abi)} items")
        except Exception as e:
            print(f"❌ Could not load contract ABI: {e}")
            return False
        
        # Check if we can create contract instance
        try:
            # This tests if the contract factory can be created
            contract_path = os.path.join('..', '..', 'CertificateRegistry.sol')
            if os.path.exists(contract_path):
                print(f"✅ Contract source file found")
            else:
                print(f"❌ Contract source file missing")
                return False
        except Exception as e:
            print(f"❌ Contract preparation failed: {e}")
            return False
        
        # Test deployment transaction parameters
        print(f"\nDeployment parameters:")
        print(f"   Chain ID: {blockchain_service.chain_id}")
        print(f"   Gas price strategy: {blockchain_service.w3.eth.gas_price}")
        print(f"   Wallet address: {blockchain_service.wallet_address}")
        
        # Check if SKALE has any special deployment requirements
        try:
            # Test if we can build a deployment transaction
            nonce = blockchain_service.w3.eth.get_transaction_count(blockchain_service.wallet_address)
            
            # Simulate deployment transaction structure
            deployment_tx = {
                'chainId': blockchain_service.chain_id,
                'gas': 3000000,
                'gasPrice': 0,  # SKALE zero gas
                'nonce': nonce,
                'from': blockchain_service.wallet_address,
                'data': '0x608060405234801561001057600080fd5b50'  # Dummy bytecode
            }
            
            print(f"✅ Deployment transaction structure valid")
            return True
            
        except Exception as e:
            print(f"❌ Deployment transaction structure invalid: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Contract deployment test failed: {e}")
        return False

def main():
    print("🌐 SKALE Europa Blockchain-Specific Issues Check")
    print("=" * 70)
    
    results = []
    
    # Run SKALE-specific tests
    results.append(("Network Specifics", test_skale_network_specifics()))
    results.append(("Transaction Format", test_skale_transaction_format()))
    results.append(("Deployment Specifics", test_contract_deployment_specifics()))
    
    # Summary
    print("\n📊 SKALE-Specific Test Results")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n✅ No SKALE-specific issues detected")
    else:
        print("\n❌ SKALE-specific issues found - This could be the deployment problem")
    
    return all_passed

if __name__ == "__main__":
    main()
