#!/usr/bin/env python3
"""
Deploy CertificateRegistry contract with your MetaMask wallet
"""
import os
import json
from web3 import Web3
from eth_account import Account
from solcx import compile_source, install_solc

def deploy_contract():
    print("🚀 Deploying CertificateRegistry with your MetaMask wallet...")
    print("=" * 60)
    
    # Your wallet configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    # Derive wallet address
    account = Account.from_key(private_key)
    wallet_address = account.address
    
    print(f"🔑 Wallet Address: {wallet_address}")
    
    # Connect to SKALE Europa
    print("🌐 Connecting to SKALE Europa...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False
    
    print("✅ Connected to SKALE Europa!")
    
    # Check balance
    balance_wei = w3.eth.get_balance(wallet_address)
    balance_ether = w3.from_wei(balance_wei, 'ether')
    print(f"💰 Wallet Balance: {balance_ether:.6f} sFUEL")
    
    if balance_ether < 0.001:
        print("⚠️  Warning: Low balance, but SKALE has zero gas fees so it should work")
    
    # Read and compile contract
    print("📝 Reading contract source...")
    contract_path = os.path.join(os.path.dirname(__file__), '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol')
    
    try:
        with open(contract_path, 'r') as f:
            contract_source = f.read()
        print("✅ Contract source loaded")
    except Exception as e:
        print(f"❌ Failed to read contract: {e}")
        return False
    
    # Install Solidity compiler
    print("🔧 Installing Solidity compiler...")
    try:
        install_solc('0.8.19')
        print("✅ Solidity compiler ready")
    except Exception as e:
        print(f"Compiler already installed: {e}")
    
    # Compile contract
    print("⚙️  Compiling contract...")
    try:
        compiled_sol = compile_source(
            contract_source,
            output_values=['abi', 'bin'],
            solc_version='0.8.19'
        )
        
        contract_id, contract_interface = compiled_sol.popitem()
        abi = contract_interface['abi']
        bytecode = contract_interface['bin']
        print("✅ Contract compiled successfully!")
        
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Deploy contract
    print("🚀 Deploying contract to blockchain...")
    try:
        # Create contract instance
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)
        
        # Get gas estimate
        gas_estimate = contract.constructor().estimate_gas()
        print(f"⛽ Estimated gas: {gas_estimate}")
        
        # Build transaction
        transaction = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': gas_estimate + 100000,  # Add buffer
            'gasPrice': 0,  # SKALE has zero gas price
            'nonce': w3.eth.get_transaction_count(wallet_address),
        })
        
        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)
        
        # Send transaction
        print("📤 Sending deployment transaction...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        print(f"📋 Transaction Hash: {tx_hash.hex()}")
        
        # Wait for receipt
        print("⏳ Waiting for deployment confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if tx_receipt.status == 1:
            contract_address = tx_receipt.contractAddress
            print("\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print("=" * 50)
            print(f"📍 Contract Address: {contract_address}")
            print(f"⛽ Gas Used: {tx_receipt.gasUsed}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{contract_address}")
            print(f"🔍 Transaction: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
            
            # Save ABI
            abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
            with open(abi_path, 'w') as f:
                json.dump(abi, f, indent=2)
            print(f"💾 ABI saved to: {abi_path}")
            
            # Update blockchain service
            update_blockchain_service(contract_address)
            
            return contract_address
        else:
            print("❌ Deployment failed!")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

def update_blockchain_service(contract_address):
    """Update blockchain service with new contract address"""
    service_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'blockchain_service.py')
    
    try:
        # Read current file
        with open(service_path, 'r') as f:
            content = f.read()
        
        # Replace the contract address line
        old_line = 'self.contract_address = None  # Will be set after deployment'
        new_line = f'self.contract_address = "{contract_address}"  # Deployed contract address'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Write updated file
            with open(service_path, 'w') as f:
                f.write(content)
            
            print(f"✅ Blockchain service updated with contract address: {contract_address}")
        else:
            print("⚠️  Could not update blockchain service automatically")
            
    except Exception as e:
        print(f"❌ Failed to update blockchain service: {e}")

if __name__ == "__main__":
    contract_address = deploy_contract()
    if contract_address:
        print(f"\n🎯 Your contract is ready!")
        print(f"📋 Contract Address: {contract_address}")
        print(f"🌐 You can now register certificates on the real blockchain!")
    else:
        print("\n❌ Deployment failed. Please check the error messages above.")
