#!/usr/bin/env python3
"""
Deploy CertificateRegistry to SKALE Europa DeFi Hub Mainnet
"""
import os
import json
from web3 import Web3
from solcx import compile_source, install_solc
from eth_account import Account

def deploy_to_europa_mainnet():
    print("🌐 SKALE Europa DeFi Hub Mainnet Deployment")
    print("=" * 50)
    
    # SKALE Europa DeFi Hub Mainnet configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    
    # Use the correct private key for the wallet
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    # Get account from private key
    account = Account.from_key(private_key)
    wallet_address = account.address
    
    print(f"📍 Wallet Address: {wallet_address}")
    print(f"🌐 Network: SKALE Europa DeFi Hub Mainnet")
    print(f"🔗 Chain ID: {chain_id}")
    
    # Initialize Web3
    print("\n🌐 Connecting to Europa mainnet...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to Europa mainnet")
        return False
    
    print("✅ Connected to Europa mainnet!")
    
    # Check balance
    balance = w3.eth.get_balance(wallet_address)
    balance_ether = w3.from_wei(balance, 'ether')
    print(f"💰 Balance: {balance_ether:.6f} sFUEL")
    
    if balance_ether < 0.005:
        print("⚠️ Warning: Low sFUEL balance for deployment")
        user_input = input("Continue anyway? (y/n): ")
        if user_input.lower() != 'y':
            return False
    
    # Install Solidity compiler
    print("\n📝 Installing Solidity compiler...")
    try:
        install_solc('0.8.19')
        print("✅ Solidity compiler ready")
    except Exception as e:
        print(f"⚠️ Compiler installation issue: {e}")
    
    # Read contract source
    # Try multiple paths
    possible_paths = [
        os.path.join(os.path.dirname(__file__), '..', '..', '..', 'CertificateRegistry.sol'),
        os.path.join(os.path.dirname(__file__), 'CertificateRegistry.sol'),
        r'C:\Users\<USER>\Desktop\Certif-Mentor-Blockchaine\Certif-Mentor-Blockchaine\Certif-Mentor-Blockchaine\CertificateRegistry.sol'
    ]

    contract_path = None
    for path in possible_paths:
        if os.path.exists(path):
            contract_path = path
            break

    if not contract_path:
        print(f"❌ Contract file not found in any of these locations:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
    print("\n📄 Reading contract source...")
    
    try:
        with open(contract_path, 'r') as f:
            contract_source = f.read()
        print("✅ Contract source loaded")
    except FileNotFoundError:
        print(f"❌ Contract file not found: {contract_path}")
        return False
    
    # Compile contract
    print("\n🔨 Compiling contract...")
    try:
        compiled_sol = compile_source(
            contract_source,
            output_values=['abi', 'bin'],
            solc_version='0.8.19'
        )
        
        contract_id, contract_interface = compiled_sol.popitem()
        abi = contract_interface['abi']
        bytecode = contract_interface['bin']
        print("✅ Contract compiled successfully")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Deploy contract
    print("\n🚀 Deploying contract to Europa mainnet...")
    print("⚠️  This will use real sFUEL tokens!")
    
    try:
        # Create contract instance
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)
        
        # Get nonce and gas price
        nonce = w3.eth.get_transaction_count(wallet_address)
        gas_price = w3.eth.gas_price
        
        print(f"⛽ Gas price: {gas_price}")
        print(f"📊 Estimated cost: ~{w3.from_wei(gas_price * 3000000, 'ether'):.6f} sFUEL")
        
        # Build transaction
        transaction = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': 3000000,
            'gasPrice': gas_price,
            'nonce': nonce,
            'from': wallet_address
        })
        
        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)
        
        # Send transaction
        print("📤 Sending transaction to mainnet...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
        print(f"📤 Transaction sent: {tx_hash.hex()}")
        
        # Wait for receipt
        print("⏳ Waiting for mainnet confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if tx_receipt.status == 1:
            contract_address = tx_receipt.contractAddress
            print("\n🎉 Contract deployed successfully to Europa mainnet!")
            print(f"   📍 Contract Address: {contract_address}")
            print(f"   🔗 Transaction Hash: {tx_hash.hex()}")
            print(f"   ⛽ Gas Used: {tx_receipt.gasUsed}")
            print(f"   💰 Cost: {w3.from_wei(tx_receipt.gasUsed * gas_price, 'ether'):.6f} sFUEL")
            print(f"   🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
            
            # Save deployment info
            save_deployment_info(abi, contract_address)
            
            return True
        else:
            print("❌ Transaction failed")
            return False
            
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        return False

def save_deployment_info(abi, contract_address):
    """Save ABI and update contract address"""
    try:
        # Save ABI
        abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
        with open(abi_path, 'w') as f:
            json.dump(abi, f, indent=2)
        print(f"✅ ABI saved to: {abi_path}")
        
        # Update blockchain service
        service_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'blockchain_service.py')
        with open(service_path, 'r') as f:
            content = f.read()
        
        # Update contract address
        updated_content = content.replace(
            'self.contract_address = None  # Will be set after mainnet deployment',
            f'self.contract_address = "{contract_address}"  # Deployed contract address'
        )
        
        with open(service_path, 'w') as f:
            f.write(updated_content)
        
        print("✅ Contract address updated in blockchain service")
        
    except Exception as e:
        print(f"⚠️ Error saving deployment info: {e}")

if __name__ == "__main__":
    print("🚀 SKALE Europa Mainnet Deployment")
    print("=" * 40)
    print("⚠️  WARNING: This will deploy to REAL mainnet!")
    print("💰 Real sFUEL tokens will be consumed!")
    print("🔒 All records will be permanent and immutable!")
    print()
    
    user_confirm = input("Are you sure you want to proceed? (yes/no): ")
    if user_confirm.lower() == 'yes':
        if deploy_to_europa_mainnet():
            print("\n🎉 Europa mainnet deployment completed successfully!")
            print("\n📋 Next steps:")
            print("   1. Restart Django server")
            print("   2. Test certificate registration on REAL blockchain")
            print("   3. Verify transactions on Europa mainnet explorer")
            print("   4. Your certificates will be permanently stored!")
        else:
            print("\n❌ Europa mainnet deployment failed")
    else:
        print("❌ Deployment cancelled by user")
