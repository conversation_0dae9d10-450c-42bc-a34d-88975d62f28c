<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>{{ mentor.name }} | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">

  <!-- Barre de navigation -->
  <header class="bg-white shadow-md py-6 px-8 flex justify-between items-center">
    <div class="text-3xl font-bold text-blue-600 cursor-pointer hover:text-blue-700 transition-colors" onclick="window.location.href='/'">CertifMentor</div>
    <div class="flex items-center gap-6">
      {% if user.is_authenticated %}
        <!-- Notification Button -->
        <button id="notificationButton" class="relative flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>
          <span class="hidden md:inline text-lg">Notifications</span>
          <span id="notificationBadge" class="notification-badge">3</span>
        </button>
        <div class="relative">
          <button id="userMenuButton" class="text-gray-700 font-medium text-lg hover:text-blue-600 transition-colors cursor-pointer">
            👤 {{ user.first_name }} {{ user.last_name }}
          </button>
        </div>
      {% else %}
        <a href="{% url 'connexion' %}" class="text-blue-600 hover:underline mr-6 text-lg">Se connecter</a>
        <a href="{% url 'inscription' %}" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
      {% endif %}
    </div>
  </header>

  <!-- Breadcrumb -->
  <div class="bg-white border-b border-gray-200 py-4 px-8">
    <nav class="text-sm text-gray-600">
      <a href="{% url 'homepage' %}" class="hover:text-blue-600">Accueil</a>
      <span class="mx-2">•</span>
      <a href="{% url 'filter' %}" class="hover:text-blue-600">Mentors</a>
      <span class="mx-2">•</span>
      <span class="text-gray-800">{{ mentor.name }}</span>
    </nav>
  </div>

  <!-- Main Content -->
  <main class="py-8 px-8">
    <div class="max-w-6xl mx-auto">

      <!-- Mentor Profile Header -->
      <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- Profile Image and Basic Info -->
          <div class="lg:w-1/3">
            <img src="{{ mentor.image }}" alt="{{ mentor.name }}" class="w-48 h-48 rounded-full mx-auto mb-6 mentor-avatar"
                 onerror="this.src='https://ui-avatars.com/api/?name={{ mentor.name|urlencode }}&size=400&background=e5e7eb&color=6b7280&bold=true'">
            <div class="text-center">
              <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ mentor.name }}</h1>
              <p class="text-lg text-gray-600 mb-4">{{ mentor.title }}</p>

              <!-- Rating and Price -->
              <div class="flex justify-center items-center gap-4 mb-6">
                <div class="flex items-center">
                  <span class="text-yellow-400 text-xl">⭐</span>
                  <span class="ml-1 font-semibold">{{ mentor.rating }}/5</span>
                  <span class="ml-1 text-gray-500">({{ mentor.reviews_count }} avis)</span>
                </div>
                <div class="text-2xl font-bold text-blue-600">{{ mentor.price }}€/h</div>
              </div>

              <!-- Hire Mentor Button -->
              <button id="hireMentorButton"
                      data-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}"
                      data-mentor-id="{{ mentor.id }}"
                      data-mentor-name="{{ mentor.name }}"
                      data-mentor-price="{{ mentor.price }}"
                      class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium text-lg">
                Hire Mentor
              </button>
            </div>
          </div>

          <!-- Detailed Information -->
          <div class="lg:w-2/3">
            <!-- About Section -->
            <div class="mb-6">
              <h2 class="text-xl font-semibold text-gray-800 mb-3">À propos</h2>
              <p class="text-gray-700 leading-relaxed">{{ mentor.description }}</p>
            </div>

            <!-- Specializations -->
            <div class="mb-6">
              <h2 class="text-xl font-semibold text-gray-800 mb-3">Spécialisations</h2>
              <div class="flex flex-wrap gap-2">
                {% for spec in mentor.specializations %}
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">{{ spec }}</span>
                {% endfor %}
              </div>
            </div>

            <!-- Experience and Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 class="font-semibold text-gray-800 mb-2">Expérience</h3>
                <p class="text-gray-600">{{ mentor.experience }} d'expérience</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-800 mb-2">Niveau</h3>
                <p class="text-gray-600">{{ mentor.level|title }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-800 mb-2">Langues</h3>
                <p class="text-gray-600">{{ mentor.languages }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-800 mb-2">Disponibilité</h3>
                <p class="text-gray-600">{{ mentor.availability_text }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Skills and Certifications -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Skills -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Compétences techniques</h2>
          <div class="space-y-3">
            {% for skill in mentor.skills %}
            <div class="flex justify-between items-center">
              <span class="text-gray-700">{{ skill.name }}</span>
              <div class="flex items-center">
                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                  <div class="bg-blue-600 h-2 rounded-full skill-bar" data-skill-level="{{ skill.level }}"></div>
                </div>
                <span class="text-sm text-gray-500">{{ skill.level }}%</span>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>

        <!-- Certifications -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Certifications</h2>
          <div class="space-y-3">
            {% for cert in mentor.certifications %}
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
              <div>
                <div class="font-medium text-gray-800">{{ cert.name }}</div>
                <div class="text-sm text-gray-500">{{ cert.issuer }} - {{ cert.year }}</div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>

      <!-- Reviews Section -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Avis des étudiants</h2>
        <div class="space-y-4">
          {% for review in mentor.reviews %}
          <div class="border-b border-gray-200 pb-4 last:border-b-0">
            <div class="flex items-center mb-2">
              <div class="flex text-yellow-400">
                {% for i in "12345" %}
                  {% if forloop.counter <= review.rating %}⭐{% else %}☆{% endif %}
                {% endfor %}
              </div>
              <span class="ml-2 font-medium text-gray-800">{{ review.student_name }}</span>
              <span class="ml-2 text-sm text-gray-500">{{ review.date }}</span>
            </div>
            <p class="text-gray-700">{{ review.comment }}</p>
          </div>
          {% endfor %}
        </div>
      </div>

    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-white text-center py-6 text-base text-gray-500 mt-12">
    &copy; 2025 CertifMentor. Tous droits réservés.
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize skill bars
      const skillBars = document.querySelectorAll('.skill-bar');
      skillBars.forEach(function(bar) {
        const skillLevel = bar.dataset.skillLevel;
        bar.style.width = skillLevel + '%';
      });

      // Hire Mentor button functionality
      const hireMentorButton = document.getElementById('hireMentorButton');

      if (hireMentorButton) {
        hireMentorButton.addEventListener('click', function() {
          const isAuthenticated = this.dataset.authenticated === 'true';
          const mentorId = this.dataset.mentorId;
          const mentorName = this.dataset.mentorName;
          const mentorPrice = this.dataset.mentorPrice;

          console.log('=== HIRE MENTOR BUTTON CLICKED ===');
          console.log('Button element:', this);
          console.log('Is authenticated:', isAuthenticated);
          console.log('Mentor ID:', mentorId);
          console.log('Mentor Name:', mentorName);
          console.log('Mentor Price:', mentorPrice);

          if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            // Redirect to login page if user is not authenticated
            window.location.href = "/connexion/";
          } else {
            // For now, show a confirmation dialog
            // Later this will be replaced with actual hiring functionality
            const confirmMessage = `Hire ${mentorName} for ${mentorPrice}€/hour?\n\nThis will redirect you to the hiring process.\n\n(Hiring functionality coming soon!)`;

            if (confirm(confirmMessage)) {
              alert(`Great! You've chosen to hire ${mentorName}.\n\nYou will be contacted soon to schedule your first session.\n\n(Hiring system in development)`);
            }
          }
        });
      } else {
        console.log('Hire Mentor button not found!');
      }
    });
  </script>

</body>
</html>
