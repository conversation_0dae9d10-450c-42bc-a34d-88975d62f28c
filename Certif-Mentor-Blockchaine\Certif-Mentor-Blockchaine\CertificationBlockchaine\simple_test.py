#!/usr/bin/env python3
"""
Simple test for Web3 connection to SKALE Europa
"""
from web3 import Web3

def test_connection():
    print("Testing SKALE Europa connection...")
    
    # SKALE Europa DeFi Hub configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    wallet_address = "******************************************"
    
    # Initialize Web3
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    # Test connection
    if w3.is_connected():
        print("✅ Connected to SKALE Europa!")
        
        # Get balance
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        print(f"Wallet balance: {balance_ether} sFUEL")
        
        # Get latest block
        latest_block = w3.eth.get_block('latest')
        print(f"Latest block: {latest_block.number}")
        
        return True
    else:
        print("❌ Failed to connect to SKALE Europa")
        return False

if __name__ == "__main__":
    test_connection()
