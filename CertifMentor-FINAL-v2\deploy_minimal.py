#!/usr/bin/env python3
"""
ULTRA MINIMAL CONTRACT - JUST TO GET SOMETHING WORKING!
"""
from web3 import Web3
from solcx import compile_source, install_solc, set_solc_version

print("🚀 ULTRA MINIMAL DEPLOYMENT")
print("=" * 40)

# Install Solc
try:
    install_solc('0.8.19')
    set_solc_version('0.8.19')
except:
    pass

# SUPER SIMPLE CONTRACT - just stores one string
CONTRACT_SOURCE = '''
pragma solidity ^0.8.19;

contract SimpleStorage {
    string public data;
    
    constructor() {
        data = "Hello Blockchain!";
    }
    
    function set(string memory _data) public {
        data = _data;
    }
}
'''

# Connect
rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
w3 = Web3(Web3.HTTPProvider(rpc_url))
print(f"✅ Connected! Block: {w3.eth.block_number}")

# Account
private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
account = w3.eth.account.from_key(bytes.fromhex(private_key_hex))
print(f"👛 Wallet: {account.address}")

# Compile
print("🔨 Compiling...")
compiled_sol = compile_source(CONTRACT_SOURCE)
contract_interface = compiled_sol['<stdin>:SimpleStorage']

# Deploy with 2M gas (being generous)
contract = w3.eth.contract(abi=contract_interface['abi'], bytecode=contract_interface['bin'])

transaction = contract.constructor().build_transaction({
    'chainId': w3.eth.chain_id,
    'gas': 2000000,  # 2M gas
    'gasPrice': w3.eth.gas_price,
    'nonce': w3.eth.get_transaction_count(account.address),
})

print(f"⛽ Gas Limit: 2,000,000")
print(f"📤 Deploying...")

# Sign and send
signed_txn = w3.eth.account.sign_transaction(transaction, bytes.fromhex(private_key_hex))
tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)

print(f"🔗 TX: {tx_hash.hex()}")
print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")

# Wait
try:
    receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
    if receipt.status == 1:
        print(f"🎉 SUCCESS!")
        print(f"📍 Contract: {receipt.contractAddress}")
        print(f"⛽ Gas Used: {receipt.gasUsed:,}")
        
        # Quick test
        deployed = w3.eth.contract(address=receipt.contractAddress, abi=contract_interface['abi'])
        data = deployed.functions.data().call()
        print(f"📋 Contract says: '{data}'")
        
    else:
        print(f"❌ Failed!")
except Exception as e:
    print(f"❌ Error: {e}")
