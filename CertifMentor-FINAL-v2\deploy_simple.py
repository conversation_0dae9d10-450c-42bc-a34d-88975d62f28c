#!/usr/bin/env python3
"""
SIMPLE CERTIFICATE DEPLOYMENT - NO HANGING!
"""
from web3 import Web3
from solcx import compile_source, install_solc, set_solc_version

print("🚀 SIMPLE CERTIFICATE DEPLOYMENT")
print("🔧 Installing Solidity compiler...")

# Install and set Solc version
try:
    install_solc('0.8.19')
    set_solc_version('0.8.19')
    print("✅ Solidity compiler ready!")
except Exception as e:
    print(f"⚠️ Solc setup warning: {e}")
    print("🔄 Continuing anyway...")
print("=" * 50)

# Minimal certificate contract
CONTRACT_SOURCE = '''
pragma solidity ^0.8.19;

contract CertificateRegistry {
    mapping(string => string) public certificates;
    address public owner;

    constructor() {
        owner = msg.sender;
    }

    function register(string memory id, string memory data) public {
        require(msg.sender == owner);
        certificates[id] = data;
    }

    function get(string memory id) public view returns (string memory) {
        return certificates[id];
    }
}
'''

# Connect to SKALE Europa
print("🔗 Connecting to SKALE Europa...")
rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
w3 = Web3(Web3.HTTPProvider(rpc_url))

if not w3.is_connected():
    print("❌ Failed to connect to SKALE Europa")
    exit(1)

print(f"✅ Connected! Latest block: {w3.eth.block_number}")

# Your private key
private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
private_key_bytes = bytes.fromhex(private_key_hex)
account = w3.eth.account.from_key(private_key_bytes)

print(f"👛 Wallet: {account.address}")

# Get balance
balance = w3.eth.get_balance(account.address)
print(f"💰 Balance: {w3.from_wei(balance, 'ether')} sFUEL")

# Compile contract
print(f"🔨 Compiling contract...")
compiled_sol = compile_source(CONTRACT_SOURCE)
contract_interface = compiled_sol['<stdin>:CertificateRegistry']

print(f"✅ Contract compiled!")

# Create contract instance
contract = w3.eth.contract(
    abi=contract_interface['abi'],
    bytecode=contract_interface['bin']
)

# Deploy with 1M gas
gas_limit = 1000000
gas_price = w3.eth.gas_price
nonce = w3.eth.get_transaction_count(account.address)

print(f"⛽ Gas Limit: {gas_limit:,}")
print(f"💰 Max Cost: {w3.from_wei(gas_limit * gas_price, 'ether')} sFUEL")

# Build transaction
transaction = contract.constructor().build_transaction({
    'chainId': w3.eth.chain_id,
    'gas': gas_limit,
    'gasPrice': gas_price,
    'nonce': nonce,
})

# Sign and send
print(f"📤 Sending transaction...")
signed_txn = w3.eth.account.sign_transaction(transaction, private_key_bytes)
tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)

print(f"✅ Transaction sent!")
print(f"🔗 TX Hash: {tx_hash.hex()}")
print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")

# Wait for confirmation
print(f"⏳ Waiting for confirmation...")
try:
    receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
    
    if receipt.status == 1:
        print(f"🎉 SUCCESS!")
        print(f"📍 Contract Address: {receipt.contractAddress}")
        print(f"⛽ Gas Used: {receipt.gasUsed:,}")
    else:
        print(f"❌ Transaction failed!")
        
except Exception as e:
    print(f"❌ Error: {e}")
