#!/usr/bin/env python3
"""
Test script to verify Ethereum Sepolia integration
"""
import os
import sys
import django

# Add the project directory to Python path
project_dir = os.path.join(os.path.dirname(__file__), 'Certif-Mentor-Blockchaine', 'Certif-Mentor-Blockchaine', 'CertificationBlockchaine')
sys.path.insert(0, project_dir)

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')

# Setup Django
django.setup()

def test_blockchain_connection():
    """Test connection to Ethereum Sepolia"""
    print("🧪 Testing Ethereum Sepolia Integration")
    print("=" * 50)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        print("✅ Blockchain service imported successfully")
        print(f"📍 Contract Address: {blockchain_service.contract_address}")
        print(f"🌐 RPC URL: {blockchain_service.rpc_url}")
        print(f"🔗 Chain ID: {blockchain_service.chain_id}")
        print(f"👛 Wallet Address: {blockchain_service.wallet_address}")
        
        # Test Web3 connection
        if blockchain_service.w3 and blockchain_service.w3.is_connected():
            print("✅ Web3 connected successfully")
            
            # Get balance
            balance_wei = blockchain_service.w3.eth.get_balance(blockchain_service.wallet_address)
            balance_eth = blockchain_service.w3.from_wei(balance_wei, 'ether')
            print(f"💰 Wallet Balance: {balance_eth:.6f} ETH")
            
            # Get latest block
            latest_block = blockchain_service.w3.eth.get_block('latest')
            print(f"📦 Latest Block: {latest_block.number}")
            
            # Test contract loading
            if blockchain_service.contract:
                print("✅ Contract loaded successfully")
                
                # Test contract functions
                try:
                    owner = blockchain_service.contract.functions.owner().call()
                    print(f"👑 Contract Owner: {owner}")
                    
                    total_certs = blockchain_service.contract.functions.totalCertificates().call()
                    print(f"📊 Total Certificates: {total_certs}")
                    
                    print("\n🎉 ALL TESTS PASSED!")
                    print("Your Django app is ready to register certificates on Ethereum Sepolia!")
                    return True
                    
                except Exception as e:
                    print(f"❌ Contract function test failed: {e}")
                    return False
            else:
                print("❌ Contract not loaded")
                return False
        else:
            print("❌ Web3 not connected")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_certificate_registration():
    """Test certificate registration"""
    print("\n🧪 Testing Certificate Registration")
    print("=" * 50)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        # Test data
        certificate_data = {
            'name': 'Test Blockchain Certificate',
            'issuer': 'Test University',
            'recipient': 'John Doe',
            'issue_date': '2024-01-15',
            'certificate_number': 'TEST-001',
            'description': 'Test certificate for blockchain integration'
        }
        
        file_content = b"This is test certificate content for blockchain registration"
        
        print("📝 Attempting to register test certificate...")
        print("⚠️  This will use real ETH on Sepolia testnet!")
        
        # Ask for confirmation
        confirm = input("Continue with registration test? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ Test cancelled by user")
            return False
        
        result = blockchain_service.register_certificate(certificate_data, file_content)
        
        if result['success']:
            print("✅ Certificate registration successful!")
            print(f"   Certificate ID: {result['certificate_id']}")
            print(f"   Transaction Hash: {result['transaction_hash']}")
            print(f"   File Hash: {result['file_hash']}")
            print(f"   Explorer: https://sepolia.etherscan.io/tx/{result['transaction_hash']}")
            return True
        else:
            print(f"❌ Certificate registration failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Ethereum Sepolia Integration Tests")
    print("=" * 60)
    
    # Test 1: Connection
    connection_ok = test_blockchain_connection()
    
    if connection_ok:
        # Test 2: Registration (optional)
        print("\n" + "=" * 60)
        test_reg = input("Run certificate registration test? (y/N): ")
        if test_reg.lower() == 'y':
            test_certificate_registration()
    
    print("\n" + "=" * 60)
    print("🏁 Tests completed!")
