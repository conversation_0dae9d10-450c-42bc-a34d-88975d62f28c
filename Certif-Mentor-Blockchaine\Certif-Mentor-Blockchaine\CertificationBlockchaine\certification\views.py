from django.shortcuts import render
from datetime import datetime, timedelta

def certification_detail(request, specialization):
    """
    View for displaying certification details for a specific specialization.
    """
    # Detailed certification data
    certifications_data = {
        'smart-contracts': {
            'name': 'Smart Contracts',
            'description': '<PERSON><PERSON><PERSON><PERSON><PERSON> le développement de contrats intelligents sur Ethereum et autres blockchains. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 299,
            'level': 'Intermédiaire',
            'prerequisites': ['Connaissance de base en programmation', 'Familiarité avec JavaScript ou Python', 'Compréhension des concepts blockchain'],
            'learning_objectives': [
                'Développer des smart contracts en Solidity',
                'Déployer des contrats sur Ethereum et testnets',
                'Implémenter des patterns de sécurité avancés',
                'Auditer et tester des smart contracts',
                'Intégrer des contrats avec des applications frontend'
            ],
            'curriculum': [
                {'module': 'Introduction aux Smart Contracts', 'topics': ['Concepts fondamentaux', 'Architecture Ethereum', 'Outils de développement']},
                {'module': 'Solidity Avancé', 'topics': ['Syntaxe et structures', 'Héritage et interfaces', 'Gestion des erreurs']},
                {'module': 'Patterns et Sécurité', 'topics': ['Patterns courants', 'Vulnérabilités communes', 'Bonnes pratiques']},
                {'module': 'Tests et Déploiement', 'topics': ['Framework de test', 'Déploiement mainnet', 'Vérification de contrats']},
                {'module': 'Projet Final', 'topics': ['DApp complète', 'Audit de sécurité', 'Présentation']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Badge LinkedIn vérifiable', 'Accès à la communauté des diplômés', 'Support carrière pendant 6 mois']
        },
        'defi': {
            'name': 'Finance Décentralisée (DeFi)',
            'description': 'Explorez l\'écosystème DeFi et maîtrisez les protocoles financiers décentralisés. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 349,
            'level': 'Avancé',
            'prerequisites': ['Expérience en smart contracts', 'Connaissance des protocoles DeFi', 'Compréhension des marchés financiers'],
            'learning_objectives': [
                'Comprendre l\'architecture des protocoles DeFi',
                'Développer des AMM et pools de liquidité',
                'Implémenter des mécanismes de yield farming',
                'Créer des systèmes de gouvernance DAO',
                'Analyser les risques et la sécurité DeFi'
            ],
            'curriculum': [
                {'module': 'Fondamentaux DeFi', 'topics': ['Écosystème DeFi', 'Protocoles majeurs', 'Tokenomics']},
                {'module': 'AMM et DEX', 'topics': ['Uniswap V3', 'Curve Finance', 'Développement AMM']},
                {'module': 'Lending et Borrowing', 'topics': ['Aave', 'Compound', 'Protocoles de prêt']},
                {'module': 'Yield Farming', 'topics': ['Stratégies de rendement', 'Vaults automatisés', 'Optimisation']},
                {'module': 'Gouvernance et DAO', 'topics': ['Systèmes de vote', 'Proposals', 'Treasury management']},
                {'module': 'Projet DeFi', 'topics': ['Protocole complet', 'Audit sécurité', 'Lancement']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Badge LinkedIn vérifiable', 'Accès aux opportunités DeFi', 'Mentorat post-formation']
        },
        'nft': {
            'name': 'NFT & Métavers',
            'description': 'Maîtrisez la création, le déploiement et la commercialisation des NFTs. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 249,
            'level': 'Débutant à Intermédiaire',
            'prerequisites': ['Connaissance de base en blockchain', 'Créativité et sens artistique', 'Notions de marketing digital'],
            'learning_objectives': [
                'Créer et déployer des collections NFT',
                'Comprendre les standards ERC-721 et ERC-1155',
                'Développer des marketplaces NFT',
                'Intégrer des NFT dans des applications métavers',
                'Stratégies de marketing et de monétisation'
            ],
            'curriculum': [
                {'module': 'Introduction aux NFT', 'topics': ['Concepts NFT', 'Standards blockchain', 'Écosystème NFT']},
                {'module': 'Création et Déploiement', 'topics': ['Smart contracts NFT', 'Métadonnées IPFS', 'Minting et distribution']},
                {'module': 'Marketplaces et Trading', 'topics': ['OpenSea', 'Développement marketplace', 'Royalties']},
                {'module': 'Métavers et Gaming', 'topics': ['Intégration Unity', 'NFT gaming', 'Économies virtuelles']},
                {'module': 'Projet NFT', 'topics': ['Collection complète', 'Stratégie marketing', 'Lancement']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Portfolio NFT', 'Accès aux communautés créatives', 'Support marketing']
        },
        'blockchain-dev': {
            'name': 'Développement Blockchain',
            'description': 'Maîtrisez le développement de blockchains, algorithmes de consensus et architectures distribuées. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 399,
            'level': 'Avancé',
            'prerequisites': ['Expérience solide en programmation', 'Connaissance des systèmes distribués', 'Compréhension cryptographique'],
            'learning_objectives': [
                'Développer une blockchain from scratch',
                'Implémenter des algorithmes de consensus',
                'Créer des réseaux peer-to-peer',
                'Optimiser les performances blockchain',
                'Sécuriser les architectures distribuées'
            ],
            'curriculum': [
                {'module': 'Architecture Blockchain', 'topics': ['Structures de données', 'Merkle trees', 'Hashing']},
                {'module': 'Algorithmes de Consensus', 'topics': ['Proof of Work', 'Proof of Stake', 'PBFT']},
                {'module': 'Réseaux P2P', 'topics': ['Protocoles réseau', 'Gossip protocols', 'Node discovery']},
                {'module': 'Optimisation et Scalabilité', 'topics': ['Sharding', 'Layer 2', 'State channels']},
                {'module': 'Sécurité Avancée', 'topics': ['Cryptographie avancée', 'Attaques réseau', 'Audit sécurité']},
                {'module': 'Projet Blockchain', 'topics': ['Blockchain complète', 'Testnet', 'Documentation']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Code source blockchain', 'Opportunités développeur senior', 'Mentorat technique']
        },
        'cryptography': {
            'name': 'Cryptographie & Sécurité',
            'description': 'Maîtrisez les algorithmes cryptographiques, signatures numériques et sécurité blockchain. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 329,
            'level': 'Avancé',
            'prerequisites': ['Mathématiques niveau universitaire', 'Programmation avancée', 'Concepts de sécurité informatique'],
            'learning_objectives': [
                'Implémenter des algorithmes cryptographiques',
                'Analyser la sécurité des protocoles blockchain',
                'Développer des systèmes de signatures avancées',
                'Auditer la sécurité des smart contracts',
                'Créer des solutions de privacy-preserving'
            ],
            'curriculum': [
                {'module': 'Cryptographie Fondamentale', 'topics': ['Chiffrement symétrique', 'Chiffrement asymétrique', 'Fonctions de hachage']},
                {'module': 'Signatures Numériques', 'topics': ['ECDSA', 'Schnorr', 'Multi-signatures']},
                {'module': 'Protocoles Zero-Knowledge', 'topics': ['zk-SNARKs', 'zk-STARKs', 'Applications privacy']},
                {'module': 'Sécurité Blockchain', 'topics': ['Audit smart contracts', 'Attaques communes', 'Formal verification']},
                {'module': 'Projet Sécurité', 'topics': ['Audit complet', 'Rapport sécurité', 'Recommandations']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Expertise reconnue', 'Opportunités audit sécurité', 'Réseau experts sécurité']
        },
        'web3': {
            'name': 'Développement Web3',
            'description': 'Maîtrisez la création d\'applications décentralisées (dApps) avec React, Web3.js et les dernières technologies. Passez notre test d\'évaluation corrigé par des mentors experts.',
            'price': 279,
            'level': 'Intermédiaire',
            'prerequisites': ['Expérience React/JavaScript', 'Connaissance HTML/CSS', 'Notions blockchain de base'],
            'learning_objectives': [
                'Développer des dApps complètes',
                'Intégrer Web3.js et Ethers.js',
                'Connecter des wallets (MetaMask, WalletConnect)',
                'Implémenter des interfaces utilisateur Web3',
                'Déployer des applications décentralisées'
            ],
            'curriculum': [
                {'module': 'Fondamentaux Web3', 'topics': ['Architecture dApp', 'Web3 vs Web2', 'Outils développement']},
                {'module': 'Intégration Blockchain', 'topics': ['Web3.js', 'Ethers.js', 'Interaction smart contracts']},
                {'module': 'Wallets et Authentification', 'topics': ['MetaMask', 'WalletConnect', 'Gestion sessions']},
                {'module': 'UI/UX Web3', 'topics': ['Design patterns Web3', 'Feedback utilisateur', 'Gestion erreurs']},
                {'module': 'Projet dApp', 'topics': ['Application complète', 'Tests utilisateur', 'Déploiement']}
            ],
            'certification_benefits': ['Certificat officiel CertifMentor', 'Portfolio dApps', 'Opportunités frontend Web3', 'Communauté développeurs']
        }
    }

    # Get certification data
    certification = certifications_data.get(specialization)
    if not certification:
        # Fallback for unknown specializations
        certification = {
            'name': specialization.replace('-', ' ').title(),
            'description': 'Test de certification en cours de développement.',
            'price': 299,
            'level': 'Tous niveaux',
            'prerequisites': ['À définir'],
            'learning_objectives': ['Contenu en cours de développement'],
            'curriculum': [],
            'certification_benefits': ['Certificat officiel CertifMentor']
        }

    context = {
        'specialization': specialization,
        'certification': certification,
    }

    return render(request, 'certification.html', context)
