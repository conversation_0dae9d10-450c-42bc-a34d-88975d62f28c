<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modifier mon Profil - CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
            CertifMentor
          </a>
        </div>

        <!-- Navigation -->
        <nav class="flex items-center space-x-6">
          <!-- Conversation Button -->
          <button id="conversationButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
            </svg>
            <span class="nav-button-text">Conversations</span>
          </button>

          <!-- Calendar Button -->
          <button id="calendrierButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            <span class="nav-button-text">Calendrier</span>
          </button>

          <!-- Mentorship Button -->
          <button id="mentorshipButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="m22 2-5 10-5-4-5 10"></path>
            </svg>
            <span class="nav-button-text">Mentorat</span>
          </button>
        </nav>

        <!-- User Menu -->
        {% if user.is_authenticated %}
          <div class="relative">
            <button id="userMenuButton" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
              <span class="mr-2 text-lg">{{ user.first_name|default:user.username }}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
              <div class="py-2">
                <div class="user-menu-item" id="profileButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  <span>Mon Profil</span>
                </div>
                <div class="user-menu-item" id="logoutButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16,17 21,12 16,7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                  <span>Déconnexion</span>
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <div class="flex items-center space-x-4">
            <a href="/connexion/" class="text-blue-600 hover:underline text-lg">Se connecter</a>
            <a href="/inscription/" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
          </div>
        {% endif %}
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-gray-900">Modifier mon Profil</h1>
          <a href="{% url 'users:profile' %}" class="text-gray-600 hover:text-gray-800 transition-colors">
            Retour au profil
          </a>
        </div>
      </div>

      <!-- Edit Form -->
      <div class="p-6">
        {% if messages %}
          {% for message in messages %}
            <div class="mb-4 p-4 rounded-lg bg-green-50 border border-green-200">
              <p class="text-green-800">{{ message }}</p>
            </div>
          {% endfor %}
        {% endif %}

        <form method="post" enctype="multipart/form-data" class="space-y-6">
          {% csrf_token %}

          <!-- Personal Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">Prénom</label>
              <input type="text" id="first_name" name="first_name" value="{{ user.first_name }}"
                     class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
              <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Nom</label>
              <input type="text" id="last_name" name="last_name" value="{{ user.last_name }}"
                     class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input type="email" id="email" name="email" value="{{ user.email }}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <div>
            <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Biographie</label>
            <textarea id="bio" name="bio" rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ profile.bio }}</textarea>
          </div>

          <!-- Contact Information -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Téléphone</label>
            <input type="tel" id="phone" name="phone" value="{{ profile.phone }}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <!-- Professional Information -->
          <div>
            <label for="profession" class="block text-sm font-medium text-gray-700 mb-2">Profession</label>
            <input type="text" id="profession" name="profession" value="{{ profile.profession }}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <div>
            <label for="experience_level" class="block text-sm font-medium text-gray-700 mb-2">Niveau d'expérience</label>
            <select id="experience_level" name="experience_level"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="beginner" {% if profile.experience_level == 'beginner' %}selected{% endif %}>Débutant</option>
              <option value="intermediate" {% if profile.experience_level == 'intermediate' %}selected{% endif %}>Intermédiaire</option>
              <option value="advanced" {% if profile.experience_level == 'advanced' %}selected{% endif %}>Avancé</option>
              <option value="expert" {% if profile.experience_level == 'expert' %}selected{% endif %}>Expert</option>
            </select>
          </div>

          <!-- Learning Information -->
          <div>
            <label for="interests" class="block text-sm font-medium text-gray-700 mb-2">Domaines d'intérêt (séparés par des virgules)</label>
            <input type="text" id="interests" name="interests" value="{{ profile.interests }}"
                   placeholder="Blockchain, Smart Contracts, DeFi, NFT..."
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <!-- Social Links -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900">Liens sociaux</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="linkedin_url" class="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
                <input type="url" id="linkedin_url" name="linkedin_url" value="{{ profile.linkedin_url }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
              <div>
                <label for="github_url" class="block text-sm font-medium text-gray-700 mb-2">GitHub</label>
                <input type="url" id="github_url" name="github_url" value="{{ profile.github_url }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
              <div>
                <label for="twitter_url" class="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
                <input type="url" id="twitter_url" name="twitter_url" value="{{ profile.twitter_url }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
              <div>
                <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">Site web</label>
                <input type="url" id="website_url" name="website_url" value="{{ profile.website_url }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>
          </div>

          <!-- Profile Image -->
          <div>
            <label for="profile_image" class="block text-sm font-medium text-gray-700 mb-2">Image de profil</label>
            <div class="flex items-start space-x-4">
              <div>
                {% if profile.profile_image %}
                  <img id="current_image" src="{{ profile.avatar_url }}" alt="Image actuelle" class="w-20 h-20 rounded-full object-cover border-2 border-gray-200">
                  <p class="text-sm text-gray-500 mt-1 text-center">Image actuelle</p>
                {% else %}
                  <img id="current_image" src="{{ profile.avatar_url }}" alt="Avatar par défaut" class="w-20 h-20 rounded-full object-cover border-2 border-gray-200">
                  <p class="text-sm text-gray-500 mt-1 text-center">Avatar par défaut</p>
                {% endif %}
              </div>
              <div class="flex-1">
                <input type="file" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                <p class="text-sm text-gray-500 mt-1">Formats acceptés: JPG, PNG, GIF (max 5MB)</p>
                <div id="image_preview" class="mt-2 hidden">
                  <img id="preview_img" src="" alt="Aperçu" class="w-20 h-20 rounded-full object-cover border-2 border-blue-200">
                  <p class="text-sm text-blue-600 mt-1">Nouvelle image</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Preferences -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900">Préférences</h3>
            <div class="space-y-3">
              <label class="flex items-center">
                <input type="checkbox" name="email_notifications" {% if profile.email_notifications %}checked{% endif %}
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700">Recevoir les notifications par email</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" name="public_profile" {% if profile.public_profile %}checked{% endif %}
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700">Profil public visible par les mentors</span>
              </label>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-4">
            <a href="{% url 'users:profile' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              Annuler
            </a>
            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Enregistrer les modifications
            </button>
          </div>
        </form>
      </div>
    </div>
  </main>

  <style>
    .nav-button {
      @apply flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors;
    }

    .nav-button-icon {
      @apply w-5 h-5;
    }

    .nav-button-text {
      @apply text-sm font-medium;
    }

    .user-menu-item {
      @apply flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer transition-colors;
    }
  </style>

  <script>
    // Navigation functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Conversation Button
      const conversationButton = document.getElementById('conversationButton');
      if (conversationButton) {
        conversationButton.addEventListener('click', function() {
          window.location.href = '/conversations/';
        });
      }

      // Calendar Button
      const calendrierButton = document.getElementById('calendrierButton');
      if (calendrierButton) {
        calendrierButton.addEventListener('click', function() {
          window.location.href = '/mentorship/calendar/';
        });
      }

      // Mentorship Button
      const mentorshipButton = document.getElementById('mentorshipButton');
      if (mentorshipButton) {
        mentorshipButton.addEventListener('click', function() {
          window.location.href = '/mentorship/';
        });
      }

      // User Menu
      const userMenuButton = document.getElementById('userMenuButton');
      const userMenu = document.getElementById('userMenu');

      if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
          e.stopPropagation();
          userMenu.classList.toggle('hidden');
        });

        document.addEventListener('click', function() {
          userMenu.classList.add('hidden');
        });

        userMenu.addEventListener('click', function(e) {
          e.stopPropagation();
        });

        // Profile functionality
        const profileButton = document.getElementById('profileButton');
        if (profileButton) {
          profileButton.addEventListener('click', function() {
            window.location.href = '/users/profile/';
          });
        }

        // Logout functionality
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
          logoutButton.addEventListener('click', function() {
            window.location.href = '/deconnexion/';
          });
        }
      }
    });

    // Image preview functionality
    function previewImage(input) {
      const preview = document.getElementById('image_preview');
      const previewImg = document.getElementById('preview_img');

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
          previewImg.src = e.target.result;
          preview.classList.remove('hidden');
        };

        reader.readAsDataURL(input.files[0]);
      } else {
        preview.classList.add('hidden');
      }
    }
  </script>
</body>
</html>
