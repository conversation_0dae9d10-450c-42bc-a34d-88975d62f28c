#!/usr/bin/env python3
"""
Simple deployment using existing blockchain service
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def main():
    print("🚀 Simple Contract Deployment")
    print("=" * 40)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        print("🔍 Checking current contract status...")
        if blockchain_service.contract_address:
            print(f"✅ Contract already deployed at: {blockchain_service.contract_address}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{blockchain_service.contract_address}")
            return blockchain_service.contract_address
        
        print("⚠️  No contract found. Attempting deployment...")
        
        # Check wallet balance
        balance = blockchain_service.get_account_balance()
        print(f"💰 Wallet balance: {balance:.6f} sFUEL")
        
        # Try to deploy
        print("🚀 Deploying contract...")
        success = blockchain_service.deploy_new_contract()
        
        if success and blockchain_service.contract_address:
            print(f"✅ Contract deployed successfully!")
            print(f"📍 Contract Address: {blockchain_service.contract_address}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{blockchain_service.contract_address}")
            return blockchain_service.contract_address
        else:
            print("❌ Deployment failed")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🎉 SUCCESS! Contract Address: {result}")
    else:
        print("\n❌ Deployment failed")
