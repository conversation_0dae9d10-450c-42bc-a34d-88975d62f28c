#!/usr/bin/env python3
"""
Pure analysis script to investigate what's actually happening with deployments
"""
import os
import sys
import django
from web3 import Web3
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def check_recent_transactions():
    """Check recent transactions from our wallet to see deployment attempts"""
    print("🔍 CHECKING RECENT DEPLOYMENT TRANSACTIONS")
    print("=" * 60)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    wallet_address = "******************************************"
    
    # Known deployment transaction hashes from our attempts
    deployment_txs = [
        "e8054e5001ced1a2ad63abf064ceaa37d542d9985fabac4be3ed42f7363423b3",  # First attempt
        "1565fba61e4f2d85a1ab5d8bde5b2f89a83a31081cbb2d5afaea70eb8f953d6b"   # Recent attempt
    ]
    
    for i, tx_hash in enumerate(deployment_txs, 1):
        print(f"\n📋 Deployment Attempt #{i}: {tx_hash}")
        print("-" * 50)
        
        try:
            # Get transaction details
            tx = w3.eth.get_transaction(tx_hash)
            print(f"   From: {tx['from']}")
            print(f"   To: {tx['to']}")  # Should be None for contract creation
            print(f"   Value: {tx['value']} wei")
            print(f"   Gas: {tx['gas']:,}")
            print(f"   Gas Price: {tx['gasPrice']}")
            print(f"   Input Data Length: {len(tx['input'])} characters")
            
            # Get transaction receipt
            receipt = w3.eth.get_transaction_receipt(tx_hash)
            print(f"   Status: {'✅ SUCCESS' if receipt['status'] == 1 else '❌ FAILED'}")
            print(f"   Gas Used: {receipt['gasUsed']:,}")
            print(f"   Contract Address: {receipt['contractAddress']}")
            
            # If contract was created, check what's deployed
            if receipt['contractAddress']:
                contract_address = receipt['contractAddress']
                print(f"\n   🔍 ANALYZING DEPLOYED CONTRACT: {contract_address}")
                
                # Check contract code
                code = w3.eth.get_code(contract_address)
                print(f"   Contract Code Length: {len(code)} bytes")
                print(f"   Contract Code (first 100 chars): {code.hex()[:100]}...")
                
                # Try to call functions we expect
                try_calling_functions(w3, contract_address)
                
            else:
                print(f"   ❌ No contract was created")
                
        except Exception as e:
            print(f"   ❌ Error checking transaction: {e}")

def try_calling_functions(w3, contract_address):
    """Try calling expected functions on the deployed contract"""
    print(f"   \n   📞 TESTING FUNCTION CALLS:")
    
    # Load the ABI that our code expects
    try:
        abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
        with open(abi_path, 'r') as f:
            expected_abi = json.load(f)
        print(f"   Expected ABI loaded: {len(expected_abi)} functions")
    except:
        print(f"   ⚠️  Could not load expected ABI")
        expected_abi = []
    
    # Try the hardcoded ABI from blockchain service
    hardcoded_abi = [
        {
            "inputs": [],
            "name": "getTotalCertificates",
            "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
            "stateMutability": "view",
            "type": "function"
        }
    ]
    
    # Test with hardcoded ABI
    print(f"   \n   Testing with hardcoded ABI:")
    try:
        contract = w3.eth.contract(address=contract_address, abi=hardcoded_abi)
        result = contract.functions.getTotalCertificates().call()
        print(f"   ✅ getTotalCertificates() = {result}")
    except Exception as e:
        print(f"   ❌ getTotalCertificates() failed: {e}")
    
    # Test with expected ABI if available
    if expected_abi:
        print(f"   \n   Testing with expected ABI:")
        try:
            contract = w3.eth.contract(address=contract_address, abi=expected_abi)
            result = contract.functions.getTotalCertificates().call()
            print(f"   ✅ getTotalCertificates() = {result}")
        except Exception as e:
            print(f"   ❌ getTotalCertificates() failed: {e}")
        
        # Try other expected functions
        try:
            exists = contract.functions.certificateExists(b'\x00' * 32).call()
            print(f"   ✅ certificateExists() = {exists}")
        except Exception as e:
            print(f"   ❌ certificateExists() failed: {e}")

def analyze_bytecode_mismatch():
    """Analyze the bytecode mismatch theory"""
    print(f"\n🔍 BYTECODE MISMATCH ANALYSIS")
    print("=" * 60)
    
    # Get the hardcoded bytecode from blockchain service
    hardcoded_bytecode = "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c80632f54bf6e1461003b578063b77bf60014610057575b600080fd5b610055600480360381019061005091906100a3565b610075565b005b61005f61008f565b60405161006c91906100df565b60405180910390f35b8060008190555050565b60005481565b600080fd5b6000819050919050565b61009d8161008a565b81146100a857600080fd5b50565b6000813590506100ba81610094565b92915050565b6000602082840312156100d6576100d5610085565b5b60006100e4848285016100ab565b91505092915050565b6100f68161008a565b82525050565b600060208201905061011160008301846100ed565b9291505056fea2646970667358221220c4c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c364736f6c63430008130033"
    
    print(f"Hardcoded bytecode length: {len(hardcoded_bytecode)} characters")
    print(f"Hardcoded bytecode (first 100): {hardcoded_bytecode[:100]}...")
    
    # Analyze what functions this bytecode actually contains
    print(f"\n🔍 ANALYZING HARDCODED BYTECODE:")
    
    # The function selectors in the bytecode
    if "2f54bf6e" in hardcoded_bytecode:
        print(f"   ✅ Found function selector: 2f54bf6e")
    if "b77bf600" in hardcoded_bytecode:
        print(f"   ✅ Found function selector: b77bf600")
    
    # Calculate what these selectors should be for our expected functions
    from web3 import Web3
    w3 = Web3()
    
    expected_functions = [
        "getTotalCertificates()",
        "registerCertificate(string,string,string,uint256,bytes32)",
        "getCertificateById(bytes32)",
        "verifyCertificateByHash(bytes32)",
        "certificateExists(bytes32)"
    ]
    
    print(f"\n📋 EXPECTED FUNCTION SELECTORS:")
    for func in expected_functions:
        selector = w3.keccak(text=func)[:4].hex()
        print(f"   {func}: {selector}")
        if selector[2:] in hardcoded_bytecode:  # Remove 0x prefix
            print(f"      ✅ Found in hardcoded bytecode")
        else:
            print(f"      ❌ NOT found in hardcoded bytecode")

def check_wallet_transaction_history():
    """Check our wallet's transaction history for all deployment attempts"""
    print(f"\n🔍 WALLET TRANSACTION HISTORY")
    print("=" * 60)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    wallet_address = "******************************************"
    
    # Get current nonce (total transactions sent)
    nonce = w3.eth.get_transaction_count(wallet_address)
    print(f"Total transactions from wallet: {nonce}")
    
    # Check recent blocks for our transactions
    latest_block = w3.eth.block_number
    print(f"Latest block: {latest_block:,}")
    
    print(f"\nLooking for contract creation transactions...")
    contract_addresses = []
    
    # Check last few blocks for our transactions
    for block_num in range(latest_block - 100, latest_block + 1):
        try:
            block = w3.eth.get_block(block_num, full_transactions=True)
            for tx in block.transactions:
                if tx['from'].lower() == wallet_address.lower() and tx['to'] is None:
                    # This is a contract creation transaction from our wallet
                    receipt = w3.eth.get_transaction_receipt(tx['hash'])
                    if receipt['contractAddress']:
                        contract_addresses.append(receipt['contractAddress'])
                        print(f"   ✅ Found contract creation: {receipt['contractAddress']}")
                        print(f"      Block: {block_num}, TX: {tx['hash'].hex()}")
        except:
            continue
    
    return contract_addresses

def main():
    print("🔍 COMPREHENSIVE DEPLOYMENT INVESTIGATION")
    print("=" * 70)
    
    # Step 1: Check known deployment transactions
    check_recent_transactions()
    
    # Step 2: Analyze bytecode mismatch
    analyze_bytecode_mismatch()
    
    # Step 3: Check wallet history
    deployed_contracts = check_wallet_transaction_history()
    
    # Summary
    print(f"\n📊 INVESTIGATION SUMMARY")
    print("=" * 40)
    print(f"Deployed contracts found: {len(deployed_contracts)}")
    for addr in deployed_contracts:
        print(f"   - {addr}")
    
    return deployed_contracts

if __name__ == "__main__":
    main()
