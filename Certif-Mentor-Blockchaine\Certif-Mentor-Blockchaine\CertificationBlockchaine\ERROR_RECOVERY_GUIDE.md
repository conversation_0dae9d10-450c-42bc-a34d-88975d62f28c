# Error Recovery Guide for Blockchain Certificate System

## Overview
This guide helps you troubleshoot and recover from common errors in the blockchain certificate system.

## Common Error Categories

### 1. Connection Errors

#### Error: "Connection refused" or "Failed to connect"
**Symptoms:**
- Cannot connect to SKALE Europa network
- Network timeouts
- RPC endpoint unreachable

**Recovery Steps:**
1. Check your internet connection
2. Verify the RPC URL is correct: `https://mainnet.skalenodes.com/v1/elated-tan-skat`
3. Try again in a few moments (network might be temporarily down)
4. Check SKALE Europa network status at [SKALE Network Status](https://status.skale.network/)

#### Error: "Request timeout"
**Symptoms:**
- Operations hang and eventually timeout
- Slow network responses

**Recovery Steps:**
1. Check network stability
2. Try again with a longer timeout
3. Use a different internet connection if possible
4. Contact your network administrator if on corporate network

### 2. Contract Errors

#### Error: "Contract not found" or "No code at address"
**Symptoms:**
- Contract calls fail
- Cannot verify certificates
- Deployment seems successful but contract doesn't work

**Recovery Steps:**
1. Verify the contract address in `blockchain_service.py`
2. Check if the contract is deployed on SKALE Europa network
3. Redeploy the contract using: `python unified_deploy.py`
4. Update the contract address in your configuration

#### Error: "Execution reverted" or "Call failed"
**Symptoms:**
- Contract function calls fail
- Certificate registration fails
- Verification returns errors

**Recovery Steps:**
1. Check the contract function parameters
2. Verify the contract state allows this operation
3. Ensure you have the correct contract ABI
4. Check if the contract function exists in the deployed contract

### 3. Transaction Errors

#### Error: "Insufficient funds" or "Not enough sFUEL"
**Symptoms:**
- Cannot deploy contracts
- Transactions fail due to low balance

**Recovery Steps:**
1. Get sFUEL tokens from the SKALE faucet
2. Visit: [SKALE Faucet](https://faucet.skale.network/)
3. Connect your wallet and request sFUEL
4. Wait for the transaction to complete
5. Note: SKALE has zero gas fees, but you still need some sFUEL for transactions

#### Error: "Nonce too low" or "Invalid nonce"
**Symptoms:**
- Transactions fail with nonce errors
- Multiple transactions seem stuck

**Recovery Steps:**
1. Wait for pending transactions to complete
2. If using MetaMask, reset the account nonce:
   - Settings → Advanced → Reset Account
3. Try the transaction again
4. Check for stuck transactions in your wallet

#### Error: "Gas limit exceeded"
**Symptoms:**
- Complex transactions fail
- Contract deployment fails

**Recovery Steps:**
1. Increase the gas limit in the transaction
2. Break down complex operations into smaller transactions
3. Optimize the contract function if possible
4. Use the default gas limits in the deployment script

### 4. Validation Errors

#### Error: "Invalid hash format"
**Symptoms:**
- Certificate verification fails
- Hash validation errors

**Recovery Steps:**
1. Ensure the hash is a valid SHA-256 (64 characters)
2. Check for typos in the hash
3. Verify the file hasn't been corrupted
4. Recalculate the file hash:
   ```bash
   # On Windows
   certutil -hashfile yourfile.pdf SHA256
   
   # On Linux/Mac
   sha256sum yourfile.pdf
   ```

#### Error: "Invalid address format"
**Symptoms:**
- Address validation fails
- Cannot interact with contracts

**Recovery Steps:**
1. Ensure the address starts with '0x'
2. Check that the address is 42 characters long
3. Verify the address checksum
4. Use a valid Ethereum address format

## Deployment Issues

### Issue: Deployment fails with "Contract source not found"
**Recovery Steps:**
1. Ensure `CertificateRegistry.sol` exists in the project
2. Check the file path in the deployment script
3. Verify file permissions
4. Try running from the correct directory

### Issue: Deployment succeeds but contract doesn't work
**Recovery Steps:**
1. Check the deployment logs for the contract address
2. Verify the contract address was updated in `blockchain_service.py`
3. Restart the Django server
4. Test with a simple contract call

### Issue: Multiple deployment scripts conflict
**Recovery Steps:**
1. Use only the unified deployment script: `python unified_deploy.py`
2. Remove or backup old deployment scripts
3. Run the migration script: `python migrate_deployment.py`

## Certificate Registration Issues

### Issue: Certificate registration fails
**Recovery Steps:**
1. Check the file format (PDF, images are supported)
2. Verify the file size is reasonable (< 10MB)
3. Ensure all required fields are filled
4. Check the blockchain connection
5. Try with a smaller test file first

### Issue: Certificate verification fails
**Recovery Steps:**
1. Verify the certificate was actually registered
2. Check the file hash matches the original
3. Ensure you're using the same file that was registered
4. Check the blockchain network connection

## Django Application Issues

### Issue: "Module not found" errors
**Recovery Steps:**
1. Ensure all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```
2. Check Python path and virtual environment
3. Restart the Django server
4. Check for import path issues

### Issue: Database errors
**Recovery Steps:**
1. Run Django migrations:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```
2. Check database permissions
3. Verify database configuration in settings.py

## Getting Help

### Log Analysis
1. Check Django logs for detailed error messages
2. Look for blockchain-specific errors in the console
3. Enable verbose logging: `python unified_deploy.py --verbose`

### Diagnostic Commands
```bash
# Test blockchain connection
python test_blockchain.py

# Check wallet balance
python get_wallet_info.py

# Test contract deployment
python unified_deploy.py --type standard

# Test certificate registration
python test_certificate_registration.py
```

### Contact Information
- Check the project documentation
- Review error logs for specific error codes
- Search for similar issues in the project repository
- Contact the development team with specific error messages and logs

## Prevention Tips

1. **Regular Backups**: Keep backups of important configuration files
2. **Test Environment**: Test changes in a development environment first
3. **Monitor Network**: Keep an eye on SKALE network status
4. **Update Dependencies**: Keep blockchain libraries up to date
5. **Validate Inputs**: Always validate user inputs before blockchain operations

## Emergency Recovery

If the system is completely broken:

1. **Backup Current State**: Save any important data
2. **Reset to Known Good State**: Use git to revert to a working commit
3. **Redeploy Contract**: Use the unified deployment script
4. **Restore Configuration**: Update all configuration files
5. **Test Thoroughly**: Verify all functionality before going live

Remember: The blockchain is immutable, so always test thoroughly before deploying to mainnet!
