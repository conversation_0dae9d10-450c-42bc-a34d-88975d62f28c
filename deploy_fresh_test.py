#!/usr/bin/env python3
"""
FRESH DEPLOYMENT TEST - NO CACHE VERSION
"""
from web3 import Web3
from solcx import compile_source, set_solc_version

# Set Solc version - BACK TO WORKING VERSION
set_solc_version('0.8.19')

# Simple test contract
CONTRACT_SOURCE = '''
pragma solidity ^0.8.19;

contract SimpleCertificate {
    address public owner;
    string public testData;

    constructor() {
        owner = msg.sender;
        testData = "Contract deployed successfully";
    }

    function setData(string memory data) public {
        require(msg.sender == owner, "Only owner can set data");
        testData = data;
    }

    function getData() public view returns (string memory) {
        return testData;
    }
}
'''

def deploy_contract():
    print("🚀 SWITCHED TO POLYGON MAINNET - SIMPLIFIED CONTRACT")
    print("=" * 40)
    
    # Connect to Polygon Mumbai Testnet (FREE!)
    rpc_url = "https://rpc-mumbai.maticvigil.com"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to Polygon Mainnet")
        return False
    
    print(f"✅ Connected to Polygon Mainnet")
    print(f"📊 Latest block: {w3.eth.block_number}")
    
    # Load wallet - FRESH KEY NO CACHE
    private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"

    # Debug: check the private key
    print(f"🔑 FRESH Private key hex: {private_key_hex}")
    print(f"🔑 FRESH Private key length: {len(private_key_hex)} chars")

    # Clean the private key (remove any non-hex characters)
    clean_key = ''.join(c for c in private_key_hex if c in '0123456789abcdefABCDEF')
    print(f"🔑 FRESH Clean key: {clean_key}")
    print(f"🔑 FRESH Clean key length: {len(clean_key)} chars")

    if len(clean_key) != 64:  # 32 bytes = 64 hex chars
        print(f"❌ Invalid private key length: {len(clean_key)} chars (expected 64)")
        return False

    try:
        private_key_bytes = bytes.fromhex(clean_key)
        print(f"✅ Private key converted: {len(private_key_bytes)} bytes")
    except ValueError as e:
        print(f"❌ Private key conversion failed: {e}")
        return False
    
    account = w3.eth.account.from_key(private_key_bytes)
    wallet_address = account.address
    
    print(f"👛 Wallet: {wallet_address}")
    
    # Get balance
    balance = w3.eth.get_balance(wallet_address)
    print(f"💰 Balance: {w3.from_wei(balance, 'ether')} MATIC")
    
    # Compile contract
    print(f"\n🔨 Compiling contract...")
    compiled_sol = compile_source(CONTRACT_SOURCE)
    contract_interface = compiled_sol['<stdin>:SimpleCertificate']
    
    print(f"✅ Contract compiled successfully!")
    print(f"📝 ABI items: {len(contract_interface['abi'])}")
    print(f"💾 Bytecode length: {len(contract_interface['bin'])} chars")
    
    # Create contract instance
    contract = w3.eth.contract(
        abi=contract_interface['abi'],
        bytecode=contract_interface['bin']
    )
    
    # Get network info
    chain_id = w3.eth.chain_id
    gas_price = w3.eth.gas_price
    nonce = w3.eth.get_transaction_count(wallet_address)
    
    print(f"\n📋 NETWORK INFO:")
    print(f"🔗 Chain ID: {chain_id}")
    print(f"⛽ Gas Price: {gas_price} wei ({w3.from_wei(gas_price, 'gwei')} Gwei)")
    print(f"🔢 Nonce: {nonce}")
    
    # Estimate gas for deployment
    try:
        gas_estimate = contract.constructor().estimate_gas()
        print(f"⛽ Gas Estimate: {gas_estimate:,}")
    except Exception as e:
        print(f"⚠️ Gas estimation failed: {e}")
        gas_estimate = 500000  # Use safe default
        print(f"⛽ Using default gas: {gas_estimate:,}")
    
    # Build transaction with VERY HIGH gas limit to test compiler theory
    gas_limit = ********  # 80M gas - much higher than estimate
    
    transaction = contract.constructor().build_transaction({
        'chainId': chain_id,
        'gas': gas_limit,
        'gasPrice': gas_price,
        'nonce': nonce,
    })
    
    print(f"\n📋 TRANSACTION:")
    print(f"⛽ Gas Limit: {gas_limit:,}")
    print(f"💰 Max Cost: {w3.from_wei(gas_limit * gas_price, 'ether')} MATIC")
    
    # Sign and send transaction
    print(f"\n🔐 Signing transaction...")
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key_bytes)
    
    print(f"📤 Sending transaction...")
    tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
    
    print(f"✅ Transaction sent!")
    print(f"🔗 TX Hash: {tx_hash.hex()}")
    print(f"🌐 Explorer: https://polygonscan.com/tx/0x{tx_hash.hex()}")
    
    # Wait for receipt
    print(f"\n⏳ Waiting for confirmation...")
    try:
        receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if receipt.status == 1:
            print(f"🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print(f"📍 Contract Address: {receipt.contractAddress}")
            print(f"⛽ Gas Used: {receipt.gasUsed:,}")
            print(f"💰 Cost: {w3.from_wei(receipt.gasUsed * gas_price, 'ether')} MATIC")
            
            # Test the contract
            deployed_contract = w3.eth.contract(
                address=receipt.contractAddress,
                abi=contract_interface['abi']
            )
            
            # Test reading owner
            owner = deployed_contract.functions.owner().call()
            print(f"👑 Contract Owner: {owner}")
            
            return True
        else:
            print(f"❌ Transaction failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error waiting for receipt: {e}")
        return False

if __name__ == "__main__":
    success = deploy_contract()
    if success:
        print(f"\n🎉 FRESH DEPLOYMENT TEST SUCCESSFUL!")
    else:
        print(f"\n❌ FRESH DEPLOYMENT TEST FAILED!")
