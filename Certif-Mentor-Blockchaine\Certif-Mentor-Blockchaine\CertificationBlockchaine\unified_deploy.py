#!/usr/bin/env python3
"""
Unified Contract Deployment Script for CertificateRegistry
Consolidates all deployment functionality with clear options and better error handling
"""
import os
import sys
import json
import argparse
from typing import Dict, Optional, Tuple
from web3 import Web3
from solcx import compile_source, install_solc
from eth_account import Account
from deployment_utils import deployment_manager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedDeployer:
    """Unified contract deployer with multiple deployment options"""
    
    def __init__(self):
        """Initialize the deployer"""
        self.rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
        self.chain_id = **********
        self.network_name = "SKALE Europa DeFi Hub"
        self.explorer_base = "https://elated-tan-skat.explorer.mainnet.skalenodes.com"
        
        # Default wallet configuration
        self.private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
        self.wallet_address = None
        self.w3 = None
        
    def setup_wallet(self, private_key: str = None) -> bool:
        """Setup wallet configuration
        
        Args:
            private_key: Optional private key override
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if private_key:
                self.private_key = private_key
            
            # Derive wallet address
            account = Account.from_key(self.private_key)
            self.wallet_address = account.address
            
            logger.info(f"🔑 Wallet configured: {self.wallet_address}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup wallet: {e}")
            return False
    
    def connect_to_network(self) -> bool:
        """Connect to SKALE Europa network
        
        Returns:
            True if connected successfully, False otherwise
        """
        try:
            logger.info(f"🌐 Connecting to {self.network_name}...")
            logger.info(f"   RPC URL: {self.rpc_url}")
            logger.info(f"   Chain ID: {self.chain_id}")
            
            # Initialize Web3 with timeout
            provider = Web3.HTTPProvider(
                self.rpc_url,
                request_kwargs={
                    'timeout': 60,
                    'headers': {
                        'Content-Type': 'application/json',
                        'User-Agent': 'CertificationBlockchain/1.0'
                    }
                }
            )
            self.w3 = Web3(provider)
            
            # Test connection
            if not self.w3.is_connected():
                raise Exception("Web3 connection failed")
            
            # Verify network
            network_chain_id = self.w3.eth.chain_id
            if network_chain_id != self.chain_id:
                raise Exception(f"Network mismatch: Expected {self.chain_id}, got {network_chain_id}")
            
            # Get latest block to confirm connectivity
            latest_block = self.w3.eth.block_number
            logger.info(f"✅ Connected to {self.network_name}")
            logger.info(f"   Latest block: {latest_block}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to network: {e}")
            return False
    
    def check_balance(self, min_balance: float = 0.001) -> Tuple[bool, float]:
        """Check wallet balance
        
        Args:
            min_balance: Minimum required balance in sFUEL
            
        Returns:
            Tuple of (sufficient_balance, actual_balance)
        """
        try:
            balance_wei = self.w3.eth.get_balance(self.wallet_address)
            balance_ether = self.w3.from_wei(balance_wei, 'ether')
            
            logger.info(f"💰 Wallet balance: {balance_ether:.6f} sFUEL")
            
            if balance_ether < min_balance:
                logger.warning(f"⚠️ Low balance detected (minimum recommended: {min_balance} sFUEL)")
                logger.info("💡 Note: SKALE has zero gas fees, so deployment should still work")
            
            return balance_ether >= min_balance, balance_ether
            
        except Exception as e:
            logger.error(f"❌ Failed to check balance: {e}")
            return False, 0.0
    
    def load_contract_source(self) -> Optional[str]:
        """Load contract source code
        
        Returns:
            Contract source code or None if failed
        """
        # Try multiple possible locations for the contract
        possible_paths = [
            os.path.join(os.path.dirname(__file__), '..', '..', '..', 'CertificateRegistry.sol'),  # Correct path
            os.path.join(os.path.dirname(__file__), '..', '..', 'CertificateRegistry.sol'),
            os.path.join(os.path.dirname(__file__), '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol'),
            os.path.join(os.path.dirname(__file__), 'CertificateRegistry.sol'),
            os.path.join(os.path.dirname(__file__), 'contracts', 'CertificateRegistry.sol')
        ]
        
        for contract_path in possible_paths:
            try:
                if os.path.exists(contract_path):
                    with open(contract_path, 'r', encoding='utf-8') as f:
                        contract_source = f.read()
                    logger.info(f"✅ Contract source loaded from: {contract_path}")
                    return contract_source
            except Exception as e:
                logger.warning(f"Failed to read contract from {contract_path}: {e}")
                continue
        
        logger.error("❌ Could not find contract source file")
        return None
    
    def compile_contract(self, contract_source: str, solc_version: str = '0.8.19') -> Optional[Tuple[list, str]]:
        """Compile the smart contract
        
        Args:
            contract_source: Solidity source code
            solc_version: Solidity compiler version
            
        Returns:
            Tuple of (abi, bytecode) or None if failed
        """
        try:
            logger.info(f"📝 Installing Solidity compiler {solc_version}...")
            install_solc(solc_version)
            
            logger.info("🔨 Compiling contract...")
            compiled_sol = compile_source(
                contract_source,
                output_values=['abi', 'bin'],
                solc_version=solc_version
            )
            
            contract_id, contract_interface = compiled_sol.popitem()
            abi = contract_interface['abi']
            bytecode = contract_interface['bin']
            
            logger.info("✅ Contract compiled successfully")
            return abi, bytecode
            
        except Exception as e:
            logger.error(f"❌ Contract compilation failed: {e}")
            return None
    
    def deploy_contract(self, abi: list, bytecode: str, gas_limit: int = 3000000, 
                       confirm_deployment: bool = True) -> Optional[str]:
        """Deploy the contract to the blockchain
        
        Args:
            abi: Contract ABI
            bytecode: Contract bytecode
            gas_limit: Gas limit for deployment
            confirm_deployment: Whether to ask for user confirmation
            
        Returns:
            Contract address if successful, None otherwise
        """
        try:
            if confirm_deployment:
                logger.info("⚠️ WARNING: This will deploy to REAL blockchain!")
                logger.info("💰 Real sFUEL tokens may be consumed!")
                logger.info("🔒 All records will be permanent and immutable!")
                
                user_confirm = input("\nAre you sure you want to proceed? (yes/no): ")
                if user_confirm.lower() != 'yes':
                    logger.info("❌ Deployment cancelled by user")
                    return None
            
            # Create contract instance
            contract = self.w3.eth.contract(abi=abi, bytecode=bytecode)
            
            # Get gas estimate
            try:
                gas_estimate = contract.constructor().estimate_gas()
                logger.info(f"⛽ Estimated gas: {gas_estimate}")
                gas_to_use = min(gas_estimate + 100000, gas_limit)  # Add buffer but respect limit
            except:
                gas_to_use = gas_limit
                logger.info(f"⛽ Using default gas limit: {gas_to_use}")
            
            # Get current gas price (should be 0 on SKALE)
            gas_price = self.w3.eth.gas_price
            logger.info(f"💰 Gas price: {gas_price} (FREE on SKALE)")
            
            # Build transaction
            transaction = contract.constructor().build_transaction({
                'chainId': self.chain_id,
                'gas': gas_to_use,
                'gasPrice': gas_price,
                'nonce': self.w3.eth.get_transaction_count(self.wallet_address),
                'from': self.wallet_address
            })
            
            # Sign transaction
            signed_txn = self.w3.eth.account.sign_transaction(transaction, self.private_key)
            
            # Send transaction
            logger.info("📤 Sending deployment transaction...")
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.raw_transaction)
            logger.info(f"📋 Transaction hash: {tx_hash.hex()}")
            
            # Wait for receipt
            logger.info("⏳ Waiting for deployment confirmation...")
            tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
            
            if tx_receipt.status == 1:
                contract_address = tx_receipt.contractAddress
                logger.info("\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
                logger.info("=" * 60)
                logger.info(f"📍 Contract Address: {contract_address}")
                logger.info(f"⛽ Gas Used: {tx_receipt.gasUsed}")
                logger.info(f"💰 Cost: {self.w3.from_wei(tx_receipt.gasUsed * gas_price, 'ether'):.6f} sFUEL")
                logger.info(f"🔗 Explorer: {self.explorer_base}/address/{contract_address}")
                logger.info(f"🔍 Transaction: {self.explorer_base}/tx/{tx_hash.hex()}")
                
                # Save deployment info using deployment manager
                deployment_success = deployment_manager.save_deployment_info(
                    abi=abi,
                    contract_address=contract_address,
                    transaction_hash=tx_hash.hex(),
                    gas_used=tx_receipt.gasUsed
                )
                
                if deployment_success:
                    logger.info("✅ All deployment information saved successfully")
                else:
                    logger.warning("⚠️ Some deployment info updates failed, but contract is deployed")
                
                return contract_address
            else:
                logger.error("❌ Deployment transaction failed")
                return None
                
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            return None

    def run_deployment(self, deployment_type: str = 'standard', private_key: str = None,
                      confirm: bool = True) -> bool:
        """Run the complete deployment process

        Args:
            deployment_type: Type of deployment ('standard', 'fresh', 'mainnet')
            private_key: Optional private key override
            confirm: Whether to ask for user confirmation

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"🚀 Starting {deployment_type} deployment to {self.network_name}")
            logger.info("=" * 60)

            # Validate environment
            validation_results = deployment_manager.validate_deployment_environment()
            if not all(validation_results.values()):
                logger.error("❌ Environment validation failed:")
                for check, result in validation_results.items():
                    status = "✅" if result else "❌"
                    logger.error(f"   {status} {check}")
                return False

            # Setup wallet
            if not self.setup_wallet(private_key):
                return False

            # Connect to network
            if not self.connect_to_network():
                return False

            # Check balance
            sufficient_balance, balance = self.check_balance()
            if not sufficient_balance and deployment_type == 'mainnet':
                logger.warning("⚠️ Insufficient balance for mainnet deployment")
                if confirm:
                    user_input = input("Continue anyway? (y/n): ")
                    if user_input.lower() != 'y':
                        return False

            # Load and compile contract
            contract_source = self.load_contract_source()
            if not contract_source:
                return False

            compilation_result = self.compile_contract(contract_source)
            if not compilation_result:
                return False

            abi, bytecode = compilation_result

            # Deploy contract
            contract_address = self.deploy_contract(abi, bytecode, confirm_deployment=confirm)
            if not contract_address:
                return False

            # Success message
            logger.info("\n🎉 Deployment completed successfully!")
            logger.info("\n📋 Next steps:")
            logger.info("   1. Restart Django server")
            logger.info("   2. Test certificate registration")
            logger.info("   3. Verify transactions on blockchain explorer")
            logger.info("   4. Your certificates will be stored on the blockchain!")

            return True

        except Exception as e:
            logger.error(f"❌ Deployment process failed: {e}")
            return False


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Unified Contract Deployment Script for CertificateRegistry',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Deployment Types:
  standard  - Standard deployment with confirmation prompts
  fresh     - Deploy a fresh contract (same as standard)
  mainnet   - Mainnet deployment with extra warnings
  auto      - Automated deployment without prompts (use with caution)

Examples:
  python unified_deploy.py                    # Standard deployment
  python unified_deploy.py --type fresh       # Fresh contract deployment
  python unified_deploy.py --type mainnet     # Mainnet deployment
  python unified_deploy.py --type auto --no-confirm  # Automated deployment
        """
    )

    parser.add_argument(
        '--type',
        choices=['standard', 'fresh', 'mainnet', 'auto'],
        default='standard',
        help='Type of deployment to perform (default: standard)'
    )

    parser.add_argument(
        '--private-key',
        help='Private key to use for deployment (overrides default)'
    )

    parser.add_argument(
        '--no-confirm',
        action='store_true',
        help='Skip confirmation prompts (use with caution)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create deployer instance
    deployer = UnifiedDeployer()

    # Determine confirmation setting
    confirm = not args.no_confirm

    # Run deployment
    success = deployer.run_deployment(
        deployment_type=args.type,
        private_key=args.private_key,
        confirm=confirm
    )

    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
