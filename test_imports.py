#!/usr/bin/env python3
"""
Test imports one by one to find the hanging issue
"""

print("🧪 TESTING IMPORTS")
print("=" * 30)

try:
    print("1. Testing basic imports...")
    import sys
    print("✅ sys imported")
    
    print("2. Testing web3 import...")
    from web3 import Web3
    print("✅ Web3 imported")
    
    print("3. Testing solcx import...")
    from solcx import compile_source, set_solc_version
    print("✅ solcx imported")
    
    print("4. Testing solc version setting...")
    set_solc_version('0.8.19')
    print("✅ solc version set")
    
    print("🎉 ALL IMPORTS SUCCESSFUL!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
