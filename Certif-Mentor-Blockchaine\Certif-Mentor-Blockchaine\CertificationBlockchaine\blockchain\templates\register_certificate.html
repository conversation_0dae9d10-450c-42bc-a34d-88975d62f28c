{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Enregistrer un Certificat | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  <script>
    // Configure Tailwind to ensure it loads
    tailwind.config = {
      theme: {
        extend: {}
      }
    }
  </script>
  <style>
    /* Fallback styles in case Tailwind doesn't load */
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f9fafb;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      background: white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      padding: 16px 32px;
      margin-bottom: 20px;
    }

    .btn {
      background: #3b82f6;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
    }

    .btn:hover {
      background: #2563eb;
    }

    .card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      padding: 24px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #374151;
    }

    .form-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 16px;
    }

    .form-input:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .blockchain-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .step-indicator {
      transition: all 0.3s ease;
      padding: 12px 24px;
      border-radius: 50px;
      background: #e5e7eb;
      color: #6b7280;
      font-weight: 600;
    }

    .step-indicator.active {
      background: #3b82f6;
      color: white;
    }

    .step-indicator.completed {
      background: #10b981;
      color: white;
    }

    .upload-area {
      border: 2px dashed #d1d5db;
      transition: all 0.3s ease;
      padding: 40px;
      text-align: center;
      border-radius: 12px;
      cursor: pointer;
    }

    .upload-area:hover {
      border-color: #3b82f6;
      background-color: #f8fafc;
    }

    .upload-area.dragover {
      border-color: #3b82f6;
      background-color: #eff6ff;
    }

    .hidden {
      display: none !important;
    }

    .text-center {
      text-align: center;
    }

    .mb-4 {
      margin-bottom: 16px;
    }

    .mb-6 {
      margin-bottom: 24px;
    }

    .py-12 {
      padding-top: 48px;
      padding-bottom: 48px;
    }

    .flex {
      display: flex;
    }

    .items-center {
      align-items: center;
    }

    .justify-center {
      justify-content: center;
    }

    .gap-4 {
      gap: 16px;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">

  <!-- Header -->
  <header class="bg-white shadow-sm py-4 px-8">
    <div class="max-w-6xl mx-auto flex justify-between items-center">
      <div class="flex items-center gap-4">
        <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700">CertifMentor</a>
        <span class="text-gray-400">|</span>
        <h1 class="text-xl font-semibold text-gray-800">Enregistrement Blockchain</h1>
      </div>
      <a href="/" class="text-gray-600 hover:text-gray-800">← Retour à l'accueil</a>
    </div>
  </header>

  <!-- Main Content -->
  <main class="max-w-4xl mx-auto py-12 px-8">
    
    <!-- Hero Section -->
    <div class="text-center mb-12">
      <div class="w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mx-auto mb-6 flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
          <circle cx="12" cy="16" r="1"></circle>
          <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
        </svg>
      </div>
      <h1 class="text-4xl font-bold text-gray-800 mb-4">Enregistrez votre certificat sur la blockchain</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Sécurisez et authentifiez votre certificat existant grâce à la technologie blockchain. 
        Une fois enregistré, votre certificat sera vérifiable à vie et impossible à falsifier.
      </p>
    </div>

    <!-- Benefits Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
      <div class="bg-white rounded-lg p-6 shadow-sm border">
        <div class="text-3xl mb-4">🔒</div>
        <h3 class="text-lg font-semibold mb-2">Sécurité Maximale</h3>
        <p class="text-gray-600">Votre certificat est protégé par la cryptographie blockchain, garantissant son intégrité.</p>
      </div>
      <div class="bg-white rounded-lg p-6 shadow-sm border">
        <div class="text-3xl mb-4">✅</div>
        <h3 class="text-lg font-semibold mb-2">Vérification Instantanée</h3>
        <p class="text-gray-600">N'importe qui peut vérifier l'authenticité de votre certificat en quelques secondes.</p>
      </div>
      <div class="bg-white rounded-lg p-6 shadow-sm border">
        <div class="text-3xl mb-4">🌍</div>
        <h3 class="text-lg font-semibold mb-2">Reconnaissance Mondiale</h3>
        <p class="text-gray-600">Votre certificat blockchain est reconnu et vérifiable partout dans le monde.</p>
      </div>
    </div>

    <!-- Step Indicators -->
    <div class="flex justify-center mb-12">
      <div class="flex items-center space-x-4">
        <div class="step-indicator active w-10 h-10 rounded-full flex items-center justify-center font-semibold">1</div>
        <div class="w-16 h-1 bg-gray-300"></div>
        <div class="step-indicator w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-semibold">2</div>
        <div class="w-16 h-1 bg-gray-300"></div>
        <div class="step-indicator w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-semibold">3</div>
      </div>
    </div>

    <!-- Registration Form -->
    <div class="bg-white rounded-lg shadow-lg p-8">
      <div id="step1" class="step-content">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Étape 1: Informations du certificat</h2>
        
        <form id="certificateForm" class="space-y-6">
          {% csrf_token %}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Nom du certificat *</label>
              <input type="text" id="certificateName" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Ex: Certification Blockchain Developer">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Organisme émetteur *</label>
              <input type="text" id="issuer" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Ex: Université de Paris">
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Date d'obtention *</label>
              <input type="date" id="issueDate" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Numéro de certificat</label>
              <input type="text" id="certificateNumber" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Ex: BC-2024-001234">
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Description du certificat</label>
            <textarea id="description" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Décrivez brièvement les compétences et connaissances validées par ce certificat..."></textarea>
          </div>

          <!-- File Upload Area -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Télécharger le certificat (PDF, JPG, PNG) *</label>
            <div class="upload-area rounded-lg p-8 text-center cursor-pointer" id="uploadArea">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-4 text-gray-400">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7,10 12,15 17,10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              <p class="text-lg font-medium text-gray-700 mb-2">Glissez-déposez votre fichier ici</p>
              <p class="text-gray-500 mb-4">ou cliquez pour sélectionner</p>
              <input type="file" id="certificateFile" accept=".pdf,.jpg,.jpeg,.png" class="hidden" required>
              <button type="button" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Choisir un fichier
              </button>
            </div>
            <div id="fileInfo" class="mt-4 hidden">
              <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600 mr-3">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                  </svg>
                  <span id="fileName" class="font-medium text-gray-700"></span>
                </div>
                <button type="button" onclick="removeFile()" class="text-red-600 hover:text-red-800">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div class="flex justify-end">
            <button type="button" onclick="nextStep()" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Continuer →
            </button>
          </div>
        </form>
      </div>

      <!-- Step 2: Verification (Hidden initially) -->
      <div id="step2" class="step-content hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Étape 2: Vérification des informations</h2>
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
          <h3 class="font-semibold text-gray-800 mb-4">Récapitulatif de votre certificat:</h3>
          <div id="summaryContent" class="space-y-2 text-gray-600">
            <!-- Summary will be populated by JavaScript -->
          </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div class="flex">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-600 mr-3 mt-0.5">
              <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <div>
              <p class="text-yellow-800 font-medium">Important</p>
              <p class="text-yellow-700 text-sm">Vérifiez attentivement toutes les informations. Une fois enregistré sur la blockchain, le certificat ne pourra plus être modifié.</p>
            </div>
          </div>
        </div>

        <div class="flex justify-between">
          <button type="button" onclick="previousStep()" class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors">
            ← Retour
          </button>
          <button type="button" onclick="nextStep()" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
            Enregistrer sur la blockchain →
          </button>
        </div>
      </div>

      <!-- Step 3: Blockchain Registration (Hidden initially) -->
      <div id="step3" class="step-content hidden">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Étape 3: Enregistrement blockchain</h2>
        
        <div id="processingState" class="text-center py-12">
          <div class="animate-spin w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-6"></div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Enregistrement en cours...</h3>
          <p class="text-gray-600">Votre certificat est en cours d'enregistrement sur la blockchain. Cela peut prendre quelques minutes.</p>
        </div>

        <div id="successState" class="text-center py-12 hidden">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-green-600 mb-4">Certificat enregistré avec succès !</h3>
          <p class="text-gray-600 mb-6">Votre certificat a été enregistré sur la blockchain avec l'ID:</p>
          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <code id="blockchainId" class="text-lg font-mono text-blue-600">0x1a2b3c4d5e6f7890abcdef1234567890</code>
          </div>

          <!-- QR Code Section -->
          <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6 max-w-md mx-auto">
            <h4 class="font-semibold text-gray-800 mb-4">QR Code de Vérification</h4>
            <div id="qrcode" class="flex justify-center mb-4"></div>
            <p class="text-sm text-gray-600">Scannez ce QR code pour vérifier rapidement l'authenticité du certificat</p>
          </div>

          <div class="space-y-4">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <div class="flex flex-col sm:flex-row gap-2">
                <button id="downloadCertBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  📄 Télécharger (TXT)
                </button>
                <button id="downloadHtmlCertBtn" class="bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-colors">
                  🌐 Télécharger (HTML)
                </button>
              </div>
              <button onclick="window.location.href='/verify-certificate/'" class="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Vérifier un autre certificat
              </button>
            </div>
            <button onclick="window.location.href='/'" class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors">
              Retour à l'accueil
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Information Section -->
    <div class="mt-12 bg-blue-50 rounded-lg p-8">
      <h3 class="text-xl font-bold text-blue-800 mb-4">Comment ça marche ?</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-700">
        <div>
          <h4 class="font-semibold mb-2">1. Hachage cryptographique</h4>
          <p class="text-sm">Votre certificat est converti en une empreinte numérique unique (hash) qui ne peut pas être falsifiée.</p>
        </div>
        <div>
          <h4 class="font-semibold mb-2">2. Enregistrement blockchain</h4>
          <p class="text-sm">Cette empreinte est enregistrée de manière permanente sur la blockchain, créant une preuve d'authenticité.</p>
        </div>
        <div>
          <h4 class="font-semibold mb-2">3. Vérification publique</h4>
          <p class="text-sm">N'importe qui peut vérifier l'authenticité de votre certificat en utilisant l'ID blockchain généré.</p>
        </div>
        <div>
          <h4 class="font-semibold mb-2">4. Sécurité garantie</h4>
          <p class="text-sm">La technologie blockchain garantit que votre certificat ne peut pas être modifié ou supprimé.</p>
        </div>
      </div>
    </div>

  </main>

  <!-- Footer -->
  <footer class="bg-white text-center py-6 text-gray-500 border-t">
    &copy; 2025 CertifMentor. Tous droits réservés.
  </footer>

  <script>
    let currentStep = 1;

    // File upload handling
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('certificateFile');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFile(files[0]);
      }
    });

    uploadArea.addEventListener('click', () => {
      fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        handleFile(e.target.files[0]);
      }
    });

    function handleFile(file) {
      fileName.textContent = file.name;
      fileInfo.classList.remove('hidden');
      uploadArea.style.display = 'none';
    }

    function removeFile() {
      fileInput.value = '';
      fileInfo.classList.add('hidden');
      uploadArea.style.display = 'block';
    }

    function updateStepIndicators() {
      const indicators = document.querySelectorAll('.step-indicator');
      indicators.forEach((indicator, index) => {
        const stepNumber = index + 1;
        indicator.classList.remove('active', 'completed');
        
        if (stepNumber < currentStep) {
          indicator.classList.add('completed');
        } else if (stepNumber === currentStep) {
          indicator.classList.add('active');
        }
      });
    }

    function showStep(step) {
      // Hide all steps
      document.querySelectorAll('.step-content').forEach(content => {
        content.classList.add('hidden');
      });
      
      // Show current step
      document.getElementById(`step${step}`).classList.remove('hidden');
      
      updateStepIndicators();
    }

    function nextStep() {
      if (currentStep === 1) {
        // Validate form
        const form = document.getElementById('certificateForm');
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }
        
        // Populate summary
        populateSummary();
        currentStep = 2;
      } else if (currentStep === 2) {
        // Start blockchain registration
        currentStep = 3;
        showStep(currentStep);
        simulateBlockchainRegistration();
        return;
      }
      
      showStep(currentStep);
    }

    function previousStep() {
      if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
      }
    }

    function populateSummary() {
      const summaryContent = document.getElementById('summaryContent');
      const certificateName = document.getElementById('certificateName').value;
      const issuer = document.getElementById('issuer').value;
      const issueDate = document.getElementById('issueDate').value;
      const certificateNumber = document.getElementById('certificateNumber').value;
      const description = document.getElementById('description').value;
      const file = document.getElementById('certificateFile').files[0];

      summaryContent.innerHTML = `
        <p><strong>Nom du certificat:</strong> ${certificateName}</p>
        <p><strong>Organisme émetteur:</strong> ${issuer}</p>
        <p><strong>Date d'obtention:</strong> ${new Date(issueDate).toLocaleDateString('fr-FR')}</p>
        ${certificateNumber ? `<p><strong>Numéro:</strong> ${certificateNumber}</p>` : ''}
        ${description ? `<p><strong>Description:</strong> ${description}</p>` : ''}
        <p><strong>Fichier:</strong> ${file ? file.name : 'Aucun fichier'}</p>
      `;
    }

    function simulateBlockchainRegistration() {
      // Actual blockchain registration
      const formData = new FormData();
      formData.append('certificate_name', document.getElementById('certificateName').value);
      formData.append('issuer', document.getElementById('issuer').value);
      formData.append('issue_date', document.getElementById('issueDate').value);
      formData.append('certificate_number', document.getElementById('certificateNumber').value);
      formData.append('description', document.getElementById('description').value);
      formData.append('certificate_file', document.getElementById('certificateFile').files[0]);
      formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

      // Debug: Log form data
      console.log('Submitting form with data:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      fetch('/register-certificate/', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        document.getElementById('processingState').classList.add('hidden');

        // Debug: Log the response to console
        console.log('Server response:', data);

        if (data.success) {
          document.getElementById('successState').classList.remove('hidden');
          document.getElementById('blockchainId').textContent = data.certificate_id;

          // Generate QR Code for verification
          generateQRCode(data.certificate_id);

          // Update download buttons with actual data
          const downloadBtn = document.getElementById('downloadCertBtn');
          const downloadHtmlBtn = document.getElementById('downloadHtmlCertBtn');

          downloadBtn.onclick = () => {
            downloadBlockchainCertificate(data, 'txt');
          };

          downloadHtmlBtn.onclick = () => {
            downloadBlockchainCertificate(data, 'html');
          };
        } else {
          // Show error
          document.getElementById('processingState').innerHTML = `
            <div class="text-center py-12">
              <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-600">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-red-600 mb-4">Erreur d'enregistrement</h3>
              <p class="text-gray-600 mb-6">${data.error}</p>
              <button onclick="previousStep()" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Retour
              </button>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById('processingState').innerHTML = `
          <div class="text-center py-12">
            <h3 class="text-2xl font-bold text-red-600 mb-4">Erreur de connexion</h3>
            <p class="text-gray-600 mb-6">Impossible de se connecter au service blockchain.</p>
            <button onclick="previousStep()" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Retour
            </button>
          </div>
        `;
      });
    }

    function generateQRCode(certificateId) {
      // Clear any existing QR code
      const qrContainer = document.getElementById('qrcode');
      qrContainer.innerHTML = '';

      // Create verification URL
      const verificationUrl = `${window.location.origin}/verify-certificate/?id=${certificateId}`;

      // Generate QR code
      QRCode.toCanvas(qrContainer, verificationUrl, {
        width: 200,
        height: 200,
        color: {
          dark: '#1f2937',  // Dark gray
          light: '#ffffff'  // White
        }
      }, function (error) {
        if (error) {
          console.error('QR Code generation failed:', error);
          qrContainer.innerHTML = '<p class="text-red-500 text-sm">Erreur lors de la génération du QR code</p>';
        }
      });
    }

    function downloadBlockchainCertificate(certificateData, format = 'txt') {
      // Get form data for certificate details
      const certificateName = document.getElementById('certificateName').value;
      const issuer = document.getElementById('issuer').value;
      const issueDate = document.getElementById('issueDate').value;
      const certificateNumber = document.getElementById('certificateNumber').value;
      const description = document.getElementById('description').value;

      let certificateContent, fileName, mimeType;

      if (format === 'html') {
        // Create HTML certificate
        certificateContent = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificat Blockchain - ${certificateName}</title>
    <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .certificate { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; text-align: center; }
        .header h1 { margin: 0; font-size: 2.5em; font-weight: bold; }
        .header p { margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9; }
        .content { padding: 40px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; margin-bottom: 20px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .info-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
        .info-label { font-weight: bold; color: #333; margin-bottom: 5px; }
        .info-value { color: #666; word-break: break-all; }
        .blockchain-id { background: #e3f2fd; border: 2px solid #2196f3; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
        .blockchain-id code { font-size: 1.1em; font-weight: bold; color: #1976d2; }
        .verification { background: #e8f5e8; border: 2px solid #4caf50; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; border-top: 1px solid #eee; }
        .qr-placeholder { width: 150px; height: 150px; background: #f0f0f0; border: 2px dashed #ccc; margin: 20px auto; display: flex; align-items: center; justify-content: center; border-radius: 10px; }
        @media print { body { background: white; } .certificate { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="header">
            <h1>🏆 CERTIFICAT BLOCKCHAIN</h1>
            <p>Authentifié et vérifié sur la blockchain Ethereum Sepolia</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📋 Informations du Certificat</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Nom du certificat</div>
                        <div class="info-value">${certificateName}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Organisme émetteur</div>
                        <div class="info-value">${issuer}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Date d'émission</div>
                        <div class="info-value">${issueDate || 'Non spécifiée'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Numéro de certificat</div>
                        <div class="info-value">${certificateNumber || 'Non spécifié'}</div>
                    </div>
                </div>
                ${description ? `<div class="info-item"><div class="info-label">Description</div><div class="info-value">${description}</div></div>` : ''}
            </div>

            <div class="blockchain-id">
                <h3 style="margin-top: 0; color: #1976d2;">🔗 ID Blockchain</h3>
                <code>${certificateData.certificate_id}</code>
            </div>

            <div class="section">
                <h2>🔐 Informations Blockchain</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Hash de transaction</div>
                        <div class="info-value">${certificateData.transaction_hash}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Hash du fichier</div>
                        <div class="info-value">${certificateData.file_hash}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Réseau blockchain</div>
                        <div class="info-value">SKALE Europa DeFi Hub</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Date d'enregistrement</div>
                        <div class="info-value">${new Date().toLocaleDateString('fr-FR')}</div>
                    </div>
                </div>
            </div>

            <div class="verification">
                <h3 style="margin-top: 0; color: #2e7d32;">✅ Vérification</h3>
                <p><strong>Statut :</strong> VÉRIFIÉ ET AUTHENTIQUE</p>
                <p><strong>Pour vérifier :</strong> Visitez <a href="${window.location.origin}/verify-certificate/" style="color: #1976d2;">${window.location.origin}/verify-certificate/</a></p>
                <p><strong>ID de vérification :</strong> ${certificateData.certificate_id}</p>
                <div class="qr-placeholder">
                    <span style="color: #999;">QR Code de vérification</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>CertifMentor - Certification Blockchain</strong></p>
            <p>Généré le ${new Date().toLocaleString('fr-FR')} | Technologie blockchain SKALE Europa</p>
            <p style="font-size: 0.9em; margin-top: 10px;">
                Ce certificat est enregistré de manière permanente et immuable sur la blockchain.<br>
                La falsification de ce document est techniquement impossible.
            </p>
        </div>
    </div>
</body>
</html>`;
        fileName = `Certificat_Blockchain_${certificateName.replace(/[^a-zA-Z0-9]/g, '_')}_${certificateData.certificate_id.substring(0, 8)}.html`;
        mimeType = 'text/html;charset=utf-8';
      } else {
        // Create text certificate
        certificateContent = `
CERTIFICAT BLOCKCHAIN AUTHENTIFIÉ
═══════════════════════════════════════════════════════════════

📋 INFORMATIONS DU CERTIFICAT
────────────────────────────────────────────────────────────────
Nom du certificat    : ${certificateName}
Organisme émetteur    : ${issuer}
Date d'émission      : ${issueDate || 'Non spécifiée'}
Numéro de certificat : ${certificateNumber || 'Non spécifié'}
Description          : ${description || 'Aucune description'}

🔗 INFORMATIONS BLOCKCHAIN
────────────────────────────────────────────────────────────────
ID Blockchain        : ${certificateData.certificate_id}
Hash de transaction  : ${certificateData.transaction_hash}
Hash du fichier      : ${certificateData.file_hash}
Réseau blockchain    : Ethereum Sepolia Testnet
Date d'enregistrement: ${new Date().toLocaleDateString('fr-FR')}
Statut               : ✅ VÉRIFIÉ ET AUTHENTIQUE

🔍 VÉRIFICATION
────────────────────────────────────────────────────────────────
Pour vérifier l'authenticité de ce certificat :

1. Visitez : ${window.location.origin}/verify-certificate/
2. Entrez l'ID : ${certificateData.certificate_id}
3. Ou scannez le QR code ci-joint

🌐 EXPLORATEUR BLOCKCHAIN
────────────────────────────────────────────────────────────────
Voir sur l'explorateur : ${certificateData.explorer_url || 'https://sepolia.etherscan.io/'}

⚠️  IMPORTANT
────────────────────────────────────────────────────────────────
Ce certificat est enregistré de manière permanente et immuable
sur la blockchain Ethereum Sepolia. Il peut être vérifié par toute
personne, à tout moment, sans autorisation spéciale.

La falsification de ce document est techniquement impossible
grâce à la technologie blockchain.

═══════════════════════════════════════════════════════════════
Généré le : ${new Date().toLocaleString('fr-FR')}
Système   : CertifMentor - Certification Blockchain
═══════════════════════════════════════════════════════════════
`;
        fileName = `Certificat_Blockchain_${certificateName.replace(/[^a-zA-Z0-9]/g, '_')}_${certificateData.certificate_id.substring(0, 8)}.txt`;
        mimeType = 'text/plain;charset=utf-8';
      }

      // Create and download the file
      const blob = new Blob([certificateContent], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      // Show success message
      const formatName = format === 'html' ? 'HTML (pour impression/PDF)' : 'TXT (texte simple)';
      alert(`📄 Certificat blockchain téléchargé avec succès!\n\nFormat: ${formatName}\n\nCe document contient toutes les informations nécessaires pour vérifier l'authenticité de votre certificat.`);
    }

    // Initialize
    showStep(currentStep);
  </script>

</body>
</html>
