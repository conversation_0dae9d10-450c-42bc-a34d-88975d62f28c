#!/usr/bin/env python3
"""
Test script to verify SKALE Europa blockchain connection
"""
import sys
import os

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_blockchain_connection():
    """Test blockchain connection and basic functionality"""
    print("🧪 Testing SKALE Europa Blockchain Connection")
    print("=" * 50)
    
    try:
        # Import blockchain service
        from blockchain.blockchain_service import blockchain_service
        
        # Test connection
        print("🌐 Testing connection...")
        balance = blockchain_service.get_account_balance()
        print(f"✅ Connected successfully!")
        print(f"   Wallet: {blockchain_service.wallet_address}")
        print(f"   Balance: {balance:.6f} sFUEL")
        print(f"   Chain ID: {blockchain_service.chain_id}")
        print(f"   RPC URL: {blockchain_service.rpc_url}")
        
        # Test file hash calculation
        print("\n🔐 Testing file hash calculation...")
        test_content = "This is a test certificate content"
        file_hash = blockchain_service.calculate_file_hash(test_content)
        print(f"✅ File hash calculated: {file_hash[:16]}...")
        
        # Test certificate registration (simulation)
        print("\n📝 Testing certificate registration...")
        test_certificate = {
            'name': 'Test Certificate',
            'issuer': 'Test University',
            'recipient': 'Test Student',
            'issue_date': **********,  # 2022-01-01
            'certificate_number': 'TEST-001',
            'description': 'This is a test certificate for blockchain integration'
        }
        
        result = blockchain_service.register_certificate(test_certificate, test_content.encode())
        
        if result['success']:
            print("✅ Certificate registration test successful!")
            print(f"   Certificate ID: {result['certificate_id'][:16]}...")
            print(f"   Transaction Hash: {result['transaction_hash'][:16]}...")
            print(f"   File Hash: {result['file_hash'][:16]}...")
        else:
            print(f"❌ Certificate registration failed: {result['error']}")
        
        print("\n🎉 All tests completed successfully!")
        print("=" * 50)
        print("Your blockchain integration is ready!")
        print("You can now register certificates on SKALE Europa.")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you have internet connection")
        print("2. Verify SKALE Europa RPC URL is accessible")
        print("3. Check if wallet address and private key are correct")
        print("4. Ensure required packages are installed: pip install web3 python-dotenv")
        return False

if __name__ == "__main__":
    success = test_blockchain_connection()
    sys.exit(0 if success else 1)
