#!/usr/bin/env python3
"""
Debug script to test blockchain service initialization
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_blockchain_service():
    print("🔍 Testing Blockchain Service Initialization")
    print("=" * 50)
    
    try:
        # Import blockchain service
        from blockchain.blockchain_service import blockchain_service
        
        print(f"✅ Blockchain service imported successfully")
        print(f"📍 RPC URL: {blockchain_service.rpc_url}")
        print(f"🔗 Chain ID: {blockchain_service.chain_id}")
        print(f"👛 Wallet Address: {blockchain_service.wallet_address}")
        print(f"📄 Contract Address: {blockchain_service.contract_address}")
        
        # Test Web3 connection
        if blockchain_service.w3:
            if blockchain_service.w3.is_connected():
                print(f"✅ Web3 connected successfully")
                
                # Get balance
                balance_wei = blockchain_service.w3.eth.get_balance(blockchain_service.wallet_address)
                balance_ether = blockchain_service.w3.from_wei(balance_wei, 'ether')
                print(f"💰 Wallet Balance: {balance_ether:.6f} sFUEL")
                
                # Get latest block
                latest_block = blockchain_service.w3.eth.get_block('latest')
                print(f"📦 Latest Block: {latest_block.number}")
                
            else:
                print(f"❌ Web3 not connected")
        else:
            print(f"❌ Web3 instance is None")
        
        # Test contract
        if blockchain_service.contract:
            print(f"✅ Contract loaded successfully")
            
            # Test contract function
            try:
                total_certs = blockchain_service.contract.functions.getTotalCertificates().call()
                print(f"📊 Total certificates on blockchain: {total_certs}")
            except Exception as e:
                print(f"❌ Contract call failed: {e}")
        else:
            print(f"❌ Contract not loaded")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_blockchain_service()
