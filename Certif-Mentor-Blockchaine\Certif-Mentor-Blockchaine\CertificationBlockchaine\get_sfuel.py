#!/usr/bin/env python3
"""
Script to help get sFUEL for SKALE Europa DeFi Hub
"""
import webbrowser
from web3 import Web3

def main():
    print("🚰 SKALE Europa sFUEL Faucet Helper")
    print("=" * 50)
    
    # Configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    wallet_address = "******************************************"
    
    # Check current balance
    print("📊 Checking current balance...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if w3.is_connected():
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        print(f"   Current balance: {balance_ether:.6f} sFUEL")
        
        if balance_ether >= 0.001:
            print("✅ You have enough sFUEL for deployment!")
            return True
        else:
            print("⚠️  You need more sFUEL for deployment")
    else:
        print("❌ Cannot connect to SKALE Europa")
        return False
    
    print("\n🚰 Getting sFUEL from Faucet...")
    print("=" * 50)
    
    # SKALE Europa faucet URLs
    faucet_urls = [
        "https://sfuel.dirtroad.dev/",
        "https://sfuel.skale.network/",
        "https://faucet.skale.network/"
    ]
    
    print("📋 Steps to get sFUEL:")
    print("1. Visit one of these faucet websites:")
    for i, url in enumerate(faucet_urls, 1):
        print(f"   {i}. {url}")
    
    print(f"\n2. Enter your wallet address: {wallet_address}")
    print("3. Select 'Europa DeFi Hub' network")
    print("4. Request sFUEL tokens")
    print("5. Wait for the transaction to complete")
    
    # Ask user if they want to open faucet
    choice = input("\n🌐 Open faucet website automatically? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        print("🌐 Opening SKALE sFUEL faucet...")
        webbrowser.open(faucet_urls[0])
        print(f"📋 Your wallet address (copy this): {wallet_address}")
        
        input("\n⏳ Press Enter after you've requested sFUEL from the faucet...")
        
        # Check balance again
        print("\n📊 Checking updated balance...")
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        print(f"   Updated balance: {balance_ether:.6f} sFUEL")
        
        if balance_ether >= 0.001:
            print("✅ Great! You now have enough sFUEL for deployment!")
            print("\n🚀 Next step: Run the deployment script")
            print("   Command: python deploy_contract.py")
            return True
        else:
            print("⚠️  Still need more sFUEL. Try another faucet or wait a bit.")
            return False
    else:
        print("\n📋 Manual steps:")
        print(f"1. Visit: {faucet_urls[0]}")
        print(f"2. Enter wallet: {wallet_address}")
        print("3. Select: Europa DeFi Hub")
        print("4. Request sFUEL")
        print("5. Run this script again to check balance")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Ready for blockchain deployment!")
    else:
        print("\n⏳ Get sFUEL first, then try again.")
