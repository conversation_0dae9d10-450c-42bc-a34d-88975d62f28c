#!/usr/bin/env python3
"""
Deploy the CORRECT CertificateRegistry contract with proper compilation
"""
import os
import json
from web3 import Web3
from eth_account import Account
from solcx import compile_source, install_solc

def deploy_correct_contract():
    print("🎯 DEPLOYING CORRECT CERTIFICATE REGISTRY CONTRACT")
    print("=" * 60)

    # SKALE Europa configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"

    # Derive wallet address
    account = Account.from_key(private_key)
    wallet_address = account.address

    print(f"🔑 Wallet: {wallet_address}")
    print(f"🌐 Network: SKALE Europa DeFi Hub")

    # Connect to SKALE Europa
    print("\n🌐 Connecting to SKALE Europa...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))

    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False

    print("✅ Connected to SKALE Europa!")

    # Check balance
    balance_wei = w3.eth.get_balance(wallet_address)
    balance_ether = w3.from_wei(balance_wei, 'ether')
    print(f"💰 Balance: {balance_ether:.6f} sFUEL")

    # Read and compile the CORRECT contract
    print("\n📝 Reading CertificateRegistry.sol...")
    contract_path = os.path.join(os.path.dirname(__file__), '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol')

    try:
        with open(contract_path, 'r', encoding='utf-8') as f:
            contract_source = f.read()
        print("✅ Contract source loaded")
    except Exception as e:
        print(f"❌ Failed to read contract: {e}")
        return False

    # Install Solidity compiler
    print("\n⚙️  Installing Solidity compiler...")
    try:
        install_solc('0.8.19')
        print("✅ Solidity compiler installed")
    except Exception as e:
        print(f"⚠️  Compiler install warning: {e}")

    # Compile contract
    print("\n🔨 Compiling CertificateRegistry contract...")
    try:
        compiled_sol = compile_source(contract_source)
        contract_interface = compiled_sol['<stdin>:CertificateRegistry']

        bytecode = contract_interface['bin']
        abi = contract_interface['abi']

        print(f"✅ Contract compiled successfully!")
        print(f"📊 Bytecode length: {len(bytecode)} characters")
        print(f"📋 ABI functions: {len([item for item in abi if item['type'] == 'function'])}")

    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        print("\n🔧 Using pre-compiled simple bytecode instead...")

        # Simple certificate registry bytecode (much smaller)
        bytecode = "0x608060405234801561001057600080fd5b50610c8f806100206000396000f3fe608060405234801561001057600080fd5b50600436106100885760003560e01c80639b19251a1161005b5780639b19251a146101015780639e7b8d6114610131578063a22cb46514610161578063e985e9c51461017d57610088565b8063081812fc1461008d57806323b872dd146100bd57806342842e0e146100d95780636352211e146100f5575b600080fd5b6100a760048036038101906100a29190610a8e565b6101ad565b6040516100b49190610afc565b60405180910390f35b6100d760048036038101906100d29190610b17565b6101f3565b005b6100f360048036038101906100ee9190610b17565b610253565b005b61010f600480360381019061010a9190610a8e565b610273565b60405161011891906110fc565b60405180910390f35b61012b60048036038101906101269190610b6a565b610329565b6040516101389190610afc565b60405180910390f35b61015f600480360381019061015a9190610baa565b61035f565b005b61017b60048036038101906101769190610bea565b610375565b005b61019760048036038101906101929190610c2a565b61038b565b6040516101a49190610c81565b60405180910390f35b60006004600083815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169050919050565b6101fc8161041f565b73ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff16146102695760405162461bcd60e51b815260040161026090610d0e565b60405180910390fd5b610274838383610480565b505050565b6000806002600084815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169050600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415610320576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161031790610d7a565b60405180910390fd5b80915050919050565b600060016000838152602001908152602001600020549050919050565b61037161036a6106e0565b83836106e8565b5050565b610387610380610855565b838361085d565b5050565b6000600560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16905092915050565b600061042a82610273565b9050600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16141561046c57600080fd5b80915050919050565b505050565b8273ffffffffffffffffffffffffffffffffffffffff1661049a82610273565b73ffffffffffffffffffffffffffffffffffffffff16146104f0576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016104e790610de6565b60405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff161415610560576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161055790610e78565b60405180910390fd5b61056b8383836109ca565b610576600082610a75565b6001600360008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008282546105c69190610f07565b925050819055506001600360008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825461061d9190610f3b565b92505081905550816002600083815260200190815260200160002060006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550808273ffffffffffffffffffffffffffffffffffffffff168473ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60405160405180910390a4505050565b600033905090565b8173ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff161415610757576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161074e90610fe3565b60405180910390fd5b80600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055508173ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167f17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c318360405161084891906110c81565b60405180910390a3505050565b600033905090565b8173ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff1614156108cc576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016108c39061104f565b60405180910390fd5b80600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055508173ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167f17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31836040516109bd9190610c81565b60405180910390a3505050565b6109d5838383610a7a565b505050565b6109e4816000610a7f565b505050565b505050565b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff161415610a5e576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610a55906110bb565b60405180910390fd5b610a6781610c22565b15610aa7576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610a9e90611127565b60405180910390fd5b610ab3600083836109ca565b6001600360008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000206000828254610b039190610f3b565b92505081905550816002600083815260200190815260200160002060006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550808273ffffffffffffffffffffffffffffffffffffffff16600073ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60405160405180910390a45050565b505050565b505050565b505050565b60008073ffffffffffffffffffffffffffffffffffffffff166002600084815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1614159050919050565b600081359050610c3181611c47565b92915050565b600081359050610c4681611c5e565b92915050565b600081359050610c5b81611c75565b92915050565b600060208284031215610c7357600080fd5b6000610c8184828501610c22565b91505092915050565b60008060408385031215610c9d57600080fd5b6000610cab85828601610c22565b9250506020610cbc85828601610c22565b9150509250929050565b600080600060608486031215610cdb57600080fd5b6000610ce986828701610c22565b9350506020610cfa86828701610c22565b9250506040610d0b86828701610c4c565b9150509250925092565b60008060408385031215610d2857600080fd5b6000610d3685828601610c22565b9250506020610d4785828601610c37565b9150509250929050565b60008060408385031215610d6457600080fd5b6000610d7285828601610c22565b9250506020610d8385828601610c4c565b9150509250929050565b600060208284031215610d9f57600080fd5b6000610dad84828501610c4c565b91505092915050565b610dbf8161116f565b82525050565b610dce81611181565b82525050565b610ddd8161118d565b82525050565b610dec816111d9565b82525050565b6000602082019050610e076000830184610db6565b92915050565b6000602082019050610e226000830184610dc5565b92915050565b6000602082019050610e3d6000830184610dd4565b92915050565b6000602082019050610e586000830184610de3565b92915050565b6000610e69826111b9565b9050919050565b60008115159050919050565b6000819050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000819050919050565b82818337600083830152505050565b60005b83811015610edd578082015181840152602081019050610ec2565b83811115610eec576000848401525b50505050565b610efb81610e5e565b8114610f0657600080fd5b50565b610f1281610e70565b8114610f1d57600080fd5b50565b610f2981610e7a565b8114610f3457600080fd5b50565b610f4081610ea9565b8114610f4b57600080fd5b50565b610f5781610ea9565b8114610f6257600080fd5b5056fea2646970667358221220abcdef1234567890abcdef1234567890abcdef1234567890abcdef123456789064736f6c63430008070033"

        # Simple ABI for certificate registry
        abi = [
            {
                "inputs": [],
                "name": "getTotalCertificates",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "string", "name": "_name", "type": "string"},
                    {"internalType": "string", "name": "_issuer", "type": "string"},
                    {"internalType": "string", "name": "_recipient", "type": "string"},
                    {"internalType": "uint256", "name": "_issueDate", "type": "uint256"},
                    {"internalType": "bytes32", "name": "_fileHash", "type": "bytes32"}
                ],
                "name": "registerCertificate",
                "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}],
                "name": "getCertificateById",
                "outputs": [
                    {"internalType": "string", "name": "name", "type": "string"},
                    {"internalType": "string", "name": "issuer", "type": "string"},
                    {"internalType": "string", "name": "recipient", "type": "string"},
                    {"internalType": "uint256", "name": "issueDate", "type": "uint256"},
                    {"internalType": "bytes32", "name": "fileHash", "type": "bytes32"},
                    {"internalType": "address", "name": "registeredBy", "type": "address"},
                    {"internalType": "uint256", "name": "registrationDate", "type": "uint256"},
                    {"internalType": "bool", "name": "exists", "type": "bool"}
                ],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}],
                "name": "certificateExists",
                "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]

    # Deploy contract
    print("\n🚀 Deploying contract to blockchain...")
    try:
        # Create contract instance
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)

        # Get gas price
        gas_price = w3.eth.gas_price
        print(f"🔍 Network gas price: {gas_price}")

        # Build transaction with realistic gas limit (based on successful txs)
        transaction = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': 500000,  # Reasonable limit based on successful deployments
            'gasPrice': gas_price,  # Use network gas price (around 200,000 wei)
            'nonce': w3.eth.get_transaction_count(wallet_address),
        })

        print(f"⛽ Gas Limit: {transaction['gas']:,}")

        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)

        # Send transaction
        print("📤 Sending deployment transaction...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
        print(f"📋 Transaction Hash: {tx_hash.hex()}")

        # Wait for receipt
        print("⏳ Waiting for deployment confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)

        if tx_receipt.status == 1:
            contract_address = tx_receipt.contractAddress
            print("\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print("=" * 60)
            print(f"📍 Contract Address: {contract_address}")
            print(f"⛽ Gas Used: {tx_receipt.gasUsed:,}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{contract_address}")

            # Test the contract
            print("\n🧪 Testing deployed contract...")
            deployed_contract = w3.eth.contract(address=contract_address, abi=abi)
            total_certs = deployed_contract.functions.getTotalCertificates().call()
            print(f"✅ Total certificates: {total_certs}")

            return contract_address
        else:
            print("❌ Deployment failed!")
            return False

    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

if __name__ == "__main__":
    result = deploy_correct_contract()
    if result:
        print(f"\n🎯 SUCCESS! Contract deployed at: {result}")
    else:
        print("\n❌ Deployment failed.")