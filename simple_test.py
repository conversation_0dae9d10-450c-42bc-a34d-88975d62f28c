#!/usr/bin/env python3
"""
Simple test to check if we're reading the correct private key
"""

def test_private_key():
    print("🧪 SIMPLE PRIVATE KEY TEST")
    print("=" * 30)
    
    # Your private key from the file
    private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    print(f"🔑 Private key: {private_key_hex}")
    print(f"🔑 Length: {len(private_key_hex)} characters")
    
    # Check if it's exactly 64 characters
    if len(private_key_hex) == 64:
        print("✅ CORRECT! Private key is exactly 64 characters")
        
        # Check if all characters are valid hex
        valid_hex = all(c in '0123456789abcdefABCDEF' for c in private_key_hex)
        if valid_hex:
            print("✅ CORRECT! All characters are valid hex")
            print("🎉 SUCCESS! This private key should work!")
            return True
        else:
            print("❌ ERROR! Contains non-hex characters")
            return False
    else:
        print(f"❌ ERROR! Expected 64 characters, got {len(private_key_hex)}")
        return False

if __name__ == "__main__":
    success = test_private_key()
    if success:
        print("\n🎯 CONCLUSION: The private key format is correct!")
        print("🔍 The problem must be folder conflicts or caching issues.")
    else:
        print("\n❌ CONCLUSION: There's an issue with the private key format.")
