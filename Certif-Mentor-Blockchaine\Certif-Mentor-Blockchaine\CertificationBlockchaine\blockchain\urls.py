from django.urls import path
from . import views

urlpatterns = [
    path('', views.homepage, name='homepage'),
    path('inscription/', views.inscription, name='inscription'),
    path('connexion/', views.connexion, name='connexion'),
    path('deconnexion/', views.deconnexion, name='deconnexion'),
    path('filter/', views.filter_mentors, name='filter'),
    path('mentor/<int:mentor_id>/', views.mentor_detail, name='mentor_detail'),
    path('register-certificate/', views.register_certificate, name='register_certificate'),
    path('test-certificate/', views.test_certificate, name='test_certificate'),
    path('verify-certificate/', views.verify_certificate, name='verify_certificate'),

    # Public API endpoints
    path('api/verify/<str:certificate_id>/', views.api_verify_certificate, name='api_verify_certificate'),
    path('api/verify-hash/', views.api_verify_certificate_hash, name='api_verify_certificate_hash'),
    path('api/info/', views.api_certificate_info, name='api_certificate_info'),
]
