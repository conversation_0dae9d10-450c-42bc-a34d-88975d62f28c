#!/usr/bin/env python3
"""
Test different gas limits to find what works on SKALE Europa
"""
from web3 import Web3
from eth_account import Account
import os

def test_gas_limits():
    print("🧪 TESTING GAS LIMITS ON SKALE EUROPA")
    print("=" * 50)
    
    # Connect to SKALE Europa
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect")
        return
    
    print("✅ Connected to SKALE Europa")
    
    # Load wallet
    private_key = os.getenv('PRIVATE_KEY')
    if not private_key:
        print("❌ No PRIVATE_KEY environment variable")
        return
    
    account = Account.from_key(private_key)
    wallet_address = account.address
    
    # Check balance
    balance = w3.eth.get_balance(wallet_address)
    balance_ether = w3.from_wei(balance, 'ether')
    print(f"💰 Balance: {balance_ether} sFUEL")
    
    if balance_ether < 0.01:
        print("❌ Insufficient balance for testing")
        return
    
    # Get network info
    chain_id = w3.eth.chain_id
    gas_price = w3.eth.gas_price
    latest_block = w3.eth.get_block('latest')
    
    print(f"🌐 Chain ID: {chain_id}")
    print(f"⛽ Gas Price: {gas_price}")
    print(f"📊 Block Gas Limit: {latest_block['gasLimit']:,}")
    
    # Simple contract bytecode (minimal)
    simple_bytecode = "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c80632e64cec11461003b5780636057361d14610059575b600080fd5b610043610075565b60405161005091906100a1565b60405180910390f35b610073600480360381019061006e91906100ed565b61007e565b005b60008054905090565b8060008190555050565b6000819050919050565b61009b81610088565b82525050565b60006020820190506100b66000830184610092565b92915050565b600080fd5b6100ca81610088565b81146100d557600080fd5b50565b6000813590506100e7816100c1565b92915050565b600060208284031215610103576101026100bc565b5b6000610111848285016100d8565b9150509291505056fea2646970667358221220a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890"
    
    # Simple ABI
    simple_abi = [
        {
            "inputs": [],
            "name": "get",
            "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
            "stateMutability": "view",
            "type": "function"
        },
        {
            "inputs": [{"internalType": "uint256", "name": "_value", "type": "uint256"}],
            "name": "set",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }
    ]
    
    # Test different gas limits
    gas_limits_to_test = [
        500000,      # 500K
        1000000,     # 1M
        2000000,     # 2M
        5000000,     # 5M
        10000000,    # 10M
        50000000,    # 50M
        100000000,   # 100M
        200000000,   # 200M (close to block limit)
    ]
    
    print(f"\n🧪 TESTING GAS LIMITS:")
    print(f"=" * 30)
    
    for gas_limit in gas_limits_to_test:
        print(f"\n⛽ Testing gas limit: {gas_limit:,}")
        
        try:
            # Create contract instance
            contract = w3.eth.contract(abi=simple_abi, bytecode=simple_bytecode)
            
            # Build transaction
            transaction = contract.constructor().build_transaction({
                'chainId': chain_id,
                'gas': gas_limit,
                'gasPrice': gas_price,
                'nonce': w3.eth.get_transaction_count(wallet_address),
                'from': wallet_address
            })
            
            # Sign transaction
            signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)
            
            # Send transaction
            tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
            print(f"   📤 TX Hash: {tx_hash.hex()}")
            
            # Wait for receipt (short timeout)
            try:
                tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=30)
                
                if tx_receipt.status == 1:
                    print(f"   ✅ SUCCESS! Gas used: {tx_receipt.gasUsed:,}")
                    print(f"   📍 Contract: {tx_receipt.contractAddress}")
                    print(f"   🎉 FOUND WORKING GAS LIMIT: {gas_limit:,}")
                    return gas_limit
                else:
                    print(f"   ❌ Transaction failed")
                    
            except Exception as e:
                print(f"   ⏰ Timeout or error: {e}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n❌ No working gas limit found")
    return None

if __name__ == "__main__":
    working_limit = test_gas_limits()
    if working_limit:
        print(f"\n🎯 RECOMMENDATION: Use gas limit of {working_limit:,} or higher")
    else:
        print(f"\n💡 Try checking the explorer for successful contract deployments to see what gas they used")
