# Generated by Django 5.2.1 on 2025-05-29 13:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_image', models.URLField(blank=True, help_text="URL de l'image de profil", null=True)),
                ('bio', models.TextField(blank=True, help_text='Biographie courte', max_length=500)),
                ('phone', models.CharField(blank=True, help_text='Numéro de téléphone', max_length=20)),
                ('location', models.CharField(blank=True, help_text='Localisation', max_length=100)),
                ('birth_date', models.DateField(blank=True, help_text='Date de naissance', null=True)),
                ('profession', models.CharField(blank=True, help_text='Profession actuelle', max_length=100)),
                ('company', models.CharField(blank=True, help_text='Entreprise', max_length=100)),
                ('experience_level', models.CharField(choices=[('beginner', 'Débutant'), ('intermediate', 'Intermédiaire'), ('advanced', 'Avancé'), ('expert', 'Expert')], default='beginner', help_text="Niveau d'expérience en blockchain", max_length=20)),
                ('learning_goals', models.TextField(blank=True, help_text="Objectifs d'apprentissage")),
                ('interests', models.TextField(blank=True, help_text="Domaines d'intérêt (séparés par des virgules)")),
                ('linkedin_url', models.URLField(blank=True, help_text='Profil LinkedIn')),
                ('github_url', models.URLField(blank=True, help_text='Profil GitHub')),
                ('twitter_url', models.URLField(blank=True, help_text='Profil Twitter')),
                ('website_url', models.URLField(blank=True, help_text='Site web personnel')),
                ('email_notifications', models.BooleanField(default=True, help_text='Recevoir les notifications par email')),
                ('public_profile', models.BooleanField(default=False, help_text='Profil public visible par les mentors')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Profil utilisateur',
                'verbose_name_plural': 'Profils utilisateurs',
            },
        ),
    ]
