from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class UserProfile(models.Model):
    """Extended user profile model"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Profile image
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True, help_text="Image de profil")

    # Personal information
    bio = models.TextField(max_length=500, blank=True, help_text="Biographie courte")
    phone = models.CharField(max_length=20, blank=True, help_text="Numéro de téléphone")
    birth_date = models.DateField(null=True, blank=True, help_text="Date de naissance")

    # Professional information
    profession = models.CharField(max_length=100, blank=True, help_text="Profession actuelle")
    experience_level = models.CharField(
        max_length=20,
        choices=[
            ('beginner', 'Débutant'),
            ('intermediate', 'Intermédiaire'),
            ('advanced', 'Avancé'),
            ('expert', 'Expert'),
        ],
        default='beginner',
        help_text="Niveau d'expérience en blockchain"
    )

    # Learning preferences
    interests = models.TextField(blank=True, help_text="Domaines d'intérêt (séparés par des virgules)")

    # Social links
    linkedin_url = models.URLField(blank=True, help_text="Profil LinkedIn")
    github_url = models.URLField(blank=True, help_text="Profil GitHub")
    twitter_url = models.URLField(blank=True, help_text="Profil Twitter")
    website_url = models.URLField(blank=True, help_text="Site web personnel")

    # Preferences
    email_notifications = models.BooleanField(default=True, help_text="Recevoir les notifications par email")
    public_profile = models.BooleanField(default=False, help_text="Profil public visible par les mentors")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Profil utilisateur"
        verbose_name_plural = "Profils utilisateurs"

    def __str__(self):
        return f"Profil de {self.user.get_full_name() or self.user.username}"

    @property
    def full_name(self):
        return self.user.get_full_name() or self.user.username

    @property
    def avatar_url(self):
        """Return profile image URL or generate a default avatar"""
        if self.profile_image:
            try:
                # Check if it's a valid uploaded file
                return self.profile_image.url
            except (ValueError, AttributeError):
                # If there's an issue with the file, fall back to default avatar
                name = self.user.get_full_name() or self.user.username
                return f"https://ui-avatars.com/api/?name={name}&size=200&background=e5e7eb&color=6b7280&bold=true"
        else:
            # Generate default avatar using UI Avatars
            name = self.user.get_full_name() or self.user.username
            return f"https://ui-avatars.com/api/?name={name}&size=200&background=e5e7eb&color=6b7280&bold=true"

    @property
    def interests_list(self):
        """Return interests as a list"""
        if self.interests:
            return [interest.strip() for interest in self.interests.split(',') if interest.strip()]
        return []

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile when a new User is created"""
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the UserProfile when the User is saved"""
    if hasattr(instance, 'profile'):
        instance.profile.save()
    else:
        UserProfile.objects.create(user=instance)
