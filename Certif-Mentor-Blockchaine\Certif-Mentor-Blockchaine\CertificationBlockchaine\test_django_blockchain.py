#!/usr/bin/env python3
"""
Test Django blockchain integration
"""
import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_django_blockchain():
    print("🧪 Testing Django Blockchain Integration")
    print("=" * 50)
    
    try:
        # Test 1: Import blockchain service in Django context
        print("1. Testing blockchain service import...")
        from blockchain.blockchain_service import blockchain_service
        
        if blockchain_service.w3 and blockchain_service.w3.is_connected():
            print("   ✅ Blockchain service connected")
        else:
            print("   ❌ Blockchain service not connected")
            return False
        
        # Test 2: Test contract
        print("2. Testing smart contract...")
        if blockchain_service.contract:
            try:
                total_certs = blockchain_service.contract.functions.getTotalCertificates().call()
                print(f"   ✅ Contract working - Total certificates: {total_certs}")
            except Exception as e:
                print(f"   ❌ Contract call failed: {e}")
                return False
        else:
            print("   ❌ Contract not loaded")
            return False
        
        # Test 3: Test Django view import
        print("3. Testing Django views...")
        from blockchain.views import register_certificate
        print("   ✅ Views imported successfully")
        
        # Test 4: Test URL resolution
        print("4. Testing URL resolution...")
        from django.urls import reverse
        try:
            url = reverse('register_certificate')
            print(f"   ✅ URL resolved: {url}")
        except Exception as e:
            print(f"   ❌ URL resolution failed: {e}")
            return False
        
        print("\n✅ All tests passed! Django blockchain integration is working.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_response():
    print("\n🌐 Testing Server Response")
    print("=" * 30)
    
    try:
        # Test if server is running
        response = requests.get('http://localhost:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ Server is responding")
            return True
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

if __name__ == "__main__":
    # Test Django integration
    django_ok = test_django_blockchain()
    
    # Test server response
    server_ok = test_server_response()
    
    if django_ok and server_ok:
        print("\n🎉 All systems operational!")
    else:
        print("\n⚠️  Some issues detected. Check the output above.")
