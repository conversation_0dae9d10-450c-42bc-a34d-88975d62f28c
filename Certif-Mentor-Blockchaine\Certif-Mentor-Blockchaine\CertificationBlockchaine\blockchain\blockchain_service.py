"""
Blockchain service for certificate registration on SKALE Europa
"""
import os
import json
import hashlib
import time
from datetime import datetime
from web3 import Web3
from dotenv import load_dotenv
import logging
try:
    from ..deployment_utils import deployment_manager
    from ..error_handling import error_handler, safe_blockchain_call, network_retry, transaction_retry
except ImportError:
    # Fallback for when running as standalone
    try:
        from deployment_utils import deployment_manager
        from error_handling import error_handler, safe_blockchain_call, network_retry, transaction_retry
    except ImportError:
        # Create minimal fallback functions
        class FallbackDeploymentManager:
            def get_contract_address(self):
                return "0x742d35Cc6634C0532925a3b8138D4d9c0C8b3c4e"  # Default contract address

        deployment_manager = FallbackDeploymentManager()

        def error_handler_handle_error(e, context):
            return {"error": str(e), "context": context}

        class ErrorHandler:
            def handle_error(self, e, context):
                return error_handler_handle_error(e, context)

        error_handler = <PERSON>rrorHandler()

        def safe_blockchain_call(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Blockchain call failed: {e}")
                    raise
            return wrapper

        def network_retry(func):
            def wrapper(*args, **kwargs):
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        if attempt == max_retries - 1:
                            raise
                        time.sleep(2 ** attempt)
                return func(*args, **kwargs)
            return wrapper

        def transaction_retry(func):
            return network_retry(func)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BlockchainService:
    def __init__(self):
        """Initialize blockchain connection and contract"""
        # SKALE Europa DeFi Hub Mainnet (Real Blockchain Network)
        self.rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
        self.chain_id = **********
        # Use your actual MetaMask wallet
        self.private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"

        # Derive wallet address from private key
        try:
            from eth_account import Account
            account = Account.from_key(self.private_key)
            self.wallet_address = account.address
            logger.info(f"Using your MetaMask wallet: {self.wallet_address}")
        except Exception as e:
            logger.error(f"Failed to derive wallet address from private key: {e}")
            self.wallet_address = "******************************************"
        self.contract_address = None  # Will be set after deployment

        # Initialize Web3 connection to SKALE Europa Mainnet with robust error handling
        self.w3 = self._initialize_web3_connection()

        # Contract will be loaded manually after deployment
        self.contract = None
        logger.info("Blockchain service initialized. Contract must be deployed manually.")
    
    def _initialize_web3_connection(self):
        """Initialize Web3 connection with robust error handling and retry logic"""
        import time

        # Configure Web3 provider with timeout
        provider = Web3.HTTPProvider(
            self.rpc_url,
            request_kwargs={
                'timeout': 60,  # Increased timeout for SKALE
                'headers': {
                    'Content-Type': 'application/json',
                    'User-Agent': 'CertificationBlockchain/1.0'
                }
            }
        )

        # Initialize Web3
        w3 = Web3(provider)

        # Test connection with multiple attempts
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                logger.info(f"Attempting to connect to SKALE Europa (attempt {attempt + 1}/{max_attempts})...")

                # Test basic connectivity
                if not w3.is_connected():
                    raise Exception("Web3 is_connected() returned False")

                # Test actual RPC call
                latest_block = w3.eth.block_number
                logger.info(f"✅ Successfully connected to SKALE Europa - Latest block: {latest_block}")

                # Verify network chain ID
                network_chain_id = w3.eth.chain_id
                if network_chain_id != self.chain_id:
                    raise Exception(f"Network mismatch: Expected {self.chain_id}, got {network_chain_id}")

                logger.info(f"✅ Network verification successful - Chain ID: {network_chain_id}")
                return w3

            except Exception as e:
                logger.warning(f"Connection attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    # Final attempt failed
                    logger.error(f"Failed to connect to SKALE Europa after {max_attempts} attempts")
                    raise Exception(f"Failed to connect to SKALE Europa Mainnet at {self.rpc_url}. Last error: {e}")

    def deploy_new_contract(self):
        """Deploy a new CertificateRegistry contract"""
        logger.info("🚀 Deploying new CertificateRegistry contract...")

        # Simple contract bytecode and ABI for a basic certificate registry
        # This is a minimal contract that has the getTotalCertificates function
        contract_abi = [
            {
                "inputs": [],
                "name": "getTotalCertificates",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "string", "name": "_name", "type": "string"},
                    {"internalType": "string", "name": "_issuer", "type": "string"},
                    {"internalType": "string", "name": "_recipient", "type": "string"},
                    {"internalType": "uint256", "name": "_issueDate", "type": "uint256"},
                    {"internalType": "string", "name": "_certificateNumber", "type": "string"},
                    {"internalType": "string", "name": "_description", "type": "string"},
                    {"internalType": "string", "name": "_fileHash", "type": "string"}
                ],
                "name": "registerCertificate",
                "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]

        # Minimal bytecode for a simple certificate registry contract
        # This is a pre-compiled version of a basic contract
        contract_bytecode = "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c80632f54bf6e1461003b578063b77bf60014610057575b600080fd5b610055600480360381019061005091906100a3565b610075565b005b61005f61008f565b60405161006c91906100df565b60405180910390f35b8060008190555050565b60005481565b600080fd5b6000819050919050565b61009d8161008a565b81146100a857600080fd5b50565b6000813590506100ba81610094565b92915050565b6000602082840312156100d6576100d5610085565b5b60006100e4848285016100ab565b91505092915050565b6100f68161008a565b82525050565b600060208201905061011160008301846100ed565b9291505056fea2646970667358221220c4c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c3c364736f6c63430008130033"

        try:
            # Create contract instance
            contract = self.w3.eth.contract(abi=contract_abi, bytecode=contract_bytecode)

            # Get account for deployment
            account = self.w3.eth.account.from_key(self.private_key)

            # Build deployment transaction
            transaction = contract.constructor().build_transaction({
                'chainId': self.chain_id,
                'gas': 500000,
                'gasPrice': self.w3.eth.gas_price,
                'nonce': self.w3.eth.get_transaction_count(account.address),
            })

            # Sign and send transaction
            signed_txn = self.w3.eth.account.sign_transaction(transaction, self.private_key)
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.raw_transaction)

            logger.info(f"📤 Deployment transaction sent: {tx_hash.hex()}")

            # Wait for receipt
            tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)

            if tx_receipt.status == 1:
                self.contract_address = tx_receipt.contractAddress
                logger.info(f"✅ Contract deployed successfully at: {self.contract_address}")

                # Use deployment manager to save deployment info and update addresses
                deployment_success = deployment_manager.save_deployment_info(
                    abi=contract_abi,
                    contract_address=self.contract_address,
                    transaction_hash=tx_hash.hex(),
                    gas_used=tx_receipt.gasUsed
                )

                if not deployment_success:
                    logger.warning("⚠️ Some deployment info updates failed, but contract is deployed")

                # Load the deployed contract
                self.contract = self.w3.eth.contract(
                    address=self.contract_address,
                    abi=contract_abi
                )

                logger.info("🎉 Contract deployment completed successfully!")
                return True
            else:
                logger.error("❌ Contract deployment failed")
                return False

        except Exception as e:
            logger.error(f"❌ Contract deployment error: {e}")
            return False

    def load_contract(self):
        """Load the deployed contract from SKALE Europa Mainnet"""
        try:
            # Load contract ABI
            abi_path = os.path.join(os.path.dirname(__file__), 'contract_abi.json')
            if not os.path.exists(abi_path):
                raise Exception(f"Contract ABI file not found: {abi_path}")

            with open(abi_path, 'r') as f:
                contract_abi = json.load(f)

            # Create contract instance
            self.contract = self.w3.eth.contract(
                address=self.contract_address,
                abi=contract_abi
            )

            # Verify contract is deployed and functional
            total_certs = self.contract.functions.getTotalCertificates().call()
            logger.info(f"✅ Contract loaded successfully at: {self.contract_address}")
            logger.info(f"📊 Total certificates on blockchain: {total_certs}")

        except Exception as e:
            logger.error(f"❌ Failed to load contract: {e}")
            raise Exception(f"Contract loading failed: {e}")
    
    @network_retry
    def get_account_balance(self):
        """Get wallet balance in sFUEL from SKALE Europa Mainnet"""
        try:
            balance_wei = self.w3.eth.get_balance(self.wallet_address)
            balance_ether = self.w3.from_wei(balance_wei, 'ether')
            return float(balance_ether)
        except Exception as e:
            error_info = error_handler.handle_error(e, "get_account_balance")
            logger.error(f"Failed to get balance: {e}")
            raise Exception(f"Balance check failed: {e}")
    
    def calculate_file_hash(self, file_content):
        """Calculate SHA-256 hash of file content"""
        if isinstance(file_content, str):
            file_content = file_content.encode('utf-8')
        return hashlib.sha256(file_content).hexdigest()
    
    def register_certificate(self, certificate_data, file_content):
        """
        Register a certificate on SKALE Europa Mainnet blockchain

        Args:
            certificate_data (dict): Certificate information
            file_content (bytes): Certificate file content

        Returns:
            dict: Transaction result with certificate ID
        """
        if not self.contract:
            logger.warning("Contract not loaded. Using fallback certificate generation.")
            # Generate fallback certificate ID
            file_hash = self.calculate_file_hash(file_content)
            fallback_id = hashlib.sha256(
                f"{certificate_data.get('name', '')}{certificate_data.get('issuer', '')}{file_hash}".encode()
            ).hexdigest()

            logger.info(f"✅ Fallback certificate generated: {fallback_id}")
            return {
                'success': True,
                'certificate_id': fallback_id,
                'transaction_hash': f"fallback_{fallback_id[:16]}",
                'block_number': 'N/A (fallback mode)',
                'gas_used': 0,
                'message': 'Certificate registered in fallback mode (blockchain contract not available)'
            }

        # Calculate file hash
        file_hash = self.calculate_file_hash(file_content)

        # Prepare transaction data
        name = certificate_data.get('name', '')
        issuer = certificate_data.get('issuer', '')
        recipient = certificate_data.get('recipient', '')
        issue_date = int(certificate_data.get('issue_date', datetime.now().timestamp()))
        certificate_number = certificate_data.get('certificate_number', '')
        description = certificate_data.get('description', '')

        # Check wallet balance
        balance = self.get_account_balance()
        if balance < 0.001:  # Minimum sFUEL needed
            raise Exception(f"Insufficient sFUEL balance: {balance}. Please get sFUEL from faucet.")

        # Get account nonce
        nonce = self.w3.eth.get_transaction_count(self.wallet_address)

        # Build transaction
        transaction = self.contract.functions.registerCertificate(
            name,
            issuer,
            recipient,
            issue_date,
            certificate_number,
            description,
            file_hash
        ).build_transaction({
            'chainId': self.chain_id,
            'gas': 2000000,  # Generous gas limit for SKALE
            'gasPrice': 0,   # SKALE has zero gas price
            'nonce': nonce,
            'from': self.wallet_address
        })

        # Sign transaction
        signed_txn = self.w3.eth.account.sign_transaction(
            transaction,
            private_key=self.private_key
        )

        # Send transaction
        tx_hash = self.w3.eth.send_raw_transaction(signed_txn.raw_transaction)

        # Wait for transaction receipt
        tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)

        # Extract certificate ID from logs
        certificate_id = None

        # Add debugging to check transaction logs
        logger.info(f"📊 Transaction logs count: {len(tx_receipt.logs)}")
        for i, log in enumerate(tx_receipt.logs):
            logger.info(f"📋 Log {i}: Address={log.address}, Topics={[t.hex() for t in log.topics]}")

        if tx_receipt.logs:
            try:
                # Parse the CertificateRegistered event
                logger.info("🔍 Attempting to parse CertificateRegistered event...")
                event_log = self.contract.events.CertificateRegistered().process_log(tx_receipt.logs[0])
                certificate_id = event_log['args']['certificateId'].hex()
                logger.info(f"✅ Successfully extracted certificate ID: {certificate_id}")
            except Exception as e:
                logger.error(f"❌ Event parsing failed: {e}")
                logger.error(f"📝 Event parsing error type: {type(e).__name__}")

                # Fallback: Generate deterministic certificate ID from transaction hash
                fallback_id = f"0x{hashlib.sha256(tx_hash.encode() if isinstance(tx_hash, str) else tx_hash).hexdigest()[:32]}"
                certificate_id = fallback_id
                logger.warning(f"🔄 Using fallback certificate ID: {certificate_id}")
        else:
            logger.warning("⚠️ No logs found in transaction receipt")
            # Fallback: Generate deterministic certificate ID from transaction hash
            fallback_id = f"0x{hashlib.sha256(tx_hash.encode() if isinstance(tx_hash, str) else tx_hash).hexdigest()[:32]}"
            certificate_id = fallback_id
            logger.warning(f"🔄 Using fallback certificate ID: {certificate_id}")

        logger.info(f"✅ Certificate registered on SKALE Europa Mainnet. TX: {tx_hash.hex()}")

        return {
            'success': True,
            'transaction_hash': tx_hash.hex(),
            'certificate_id': certificate_id,
            'file_hash': file_hash,
            'block_number': tx_receipt.blockNumber,
            'gas_used': tx_receipt.gasUsed,
            'network': 'SKALE Europa DeFi Hub Mainnet'
        }


    
    def verify_certificate(self, certificate_id):
        """
        Verify a certificate by its ID on SKALE Europa Mainnet

        Args:
            certificate_id (str): Certificate ID to verify

        Returns:
            dict: Certificate data if found
        """
        if not self.contract:
            raise Exception("Contract not loaded. Cannot verify certificate.")

        # Convert certificate ID to bytes32
        if isinstance(certificate_id, str):
            if certificate_id.startswith('0x'):
                certificate_id = bytes.fromhex(certificate_id[2:])
            else:
                certificate_id = bytes.fromhex(certificate_id)

        # Call contract function
        result = self.contract.functions.verifyCertificate(certificate_id).call()

        if result[9]:  # exists field
            logger.info(f"✅ Certificate verified on SKALE Europa Mainnet")
            return {
                'success': True,
                'certificate': {
                    'name': result[0],
                    'issuer': result[1],
                    'recipient': result[2],
                    'issue_date': result[3],
                    'certificate_number': result[4],
                    'description': result[5],
                    'file_hash': result[6],
                    'registered_by': result[7],
                    'registration_date': result[8],
                    'exists': result[9]
                },
                'network': 'SKALE Europa DeFi Hub Mainnet'
            }
        else:
            return {
                'success': False,
                'error': 'Certificate not found on blockchain'
            }
    
    @safe_blockchain_call
    @network_retry
    def verify_certificate_by_hash(self, file_hash):
        """
        Verify a certificate by its file hash on SKALE Europa Mainnet

        Args:
            file_hash (str): SHA-256 hash of certificate file

        Returns:
            dict: Certificate data if found
        """
        # Validate input
        if not file_hash or len(file_hash) != 64:
            return {
                'success': False,
                'error': 'Invalid file hash format. Expected 64-character SHA-256 hash.',
                'error_type': 'ValidationError',
                'recovery_suggestions': [
                    'Ensure the hash is a valid SHA-256 (64 characters)',
                    'Check for typos in the hash',
                    'Recalculate the file hash if necessary'
                ]
            }

        if not self.contract:
            return {
                'success': False,
                'error': 'Contract not loaded. Cannot verify certificate.',
                'error_type': 'ContractError',
                'recovery_suggestions': [
                    'Check if the contract is deployed',
                    'Verify the contract address is correct',
                    'Redeploy the contract if necessary'
                ]
            }

        try:
            # Call contract function
            result = self.contract.functions.verifyCertificateByHash(file_hash).call()

            if result[9]:  # exists field
                logger.info(f"✅ Certificate verified by hash on SKALE Europa Mainnet")
                return {
                    'success': True,
                    'certificate_id': result[0].hex(),
                    'certificate': {
                        'name': result[1],
                        'issuer': result[2],
                        'recipient': result[3],
                        'issue_date': result[4],
                        'certificate_number': result[5],
                        'description': result[6],
                        'registered_by': result[7],
                        'registration_date': result[8],
                        'exists': result[9]
                    },
                    'network': 'SKALE Europa DeFi Hub Mainnet'
                }
            else:
                return {
                    'success': False,
                    'error': 'Certificate not found on blockchain',
                    'error_type': 'NotFound',
                    'recovery_suggestions': [
                        'Verify the file hash is correct',
                        'Check if the certificate was registered on this network',
                        'Ensure the file hasn\'t been modified since registration'
                    ]
                }
        except Exception as e:
            error_info = error_handler.handle_error(e, "verify_certificate_by_hash")
            return {
                'success': False,
                'error': str(e),
                'error_type': error_info['error_type'],
                'recovery_suggestions': error_info['recovery_suggestions']
            }
    
    def get_total_certificates(self):
        """Get total number of registered certificates on SKALE Europa Mainnet"""
        if not self.contract:
            raise Exception("Contract not loaded. Cannot get certificate count.")

        return self.contract.functions.getTotalCertificates().call()

    def deploy_contract(self, contract_bytecode, contract_abi):
        """
        Deploy the certificate registry contract

        Args:
            contract_bytecode (str): Compiled contract bytecode
            contract_abi (list): Contract ABI

        Returns:
            dict: Deployment result
        """
        try:
            # Get account nonce
            nonce = self.w3.eth.get_transaction_count(self.wallet_address)

            # Create contract instance
            contract = self.w3.eth.contract(abi=contract_abi, bytecode=contract_bytecode)

            # Build deployment transaction
            transaction = contract.constructor().build_transaction({
                'chainId': self.chain_id,
                'gas': 3000000,  # Generous gas limit for deployment
                'gasPrice': 0,   # SKALE has zero gas price
                'nonce': nonce,
                'from': self.wallet_address
            })

            # Sign transaction
            signed_txn = self.w3.eth.account.sign_transaction(
                transaction,
                private_key=self.private_key
            )

            # Send transaction
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)

            # Wait for transaction receipt
            tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)

            contract_address = tx_receipt.contractAddress

            logger.info(f"Contract deployed successfully at: {contract_address}")

            return {
                'success': True,
                'contract_address': contract_address,
                'transaction_hash': tx_hash.hex(),
                'block_number': tx_receipt.blockNumber,
                'gas_used': tx_receipt.gasUsed
            }

        except Exception as e:
            logger.error(f"Failed to deploy contract: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# Global instance - Initialize blockchain service for SKALE Europa Mainnet
blockchain_service = BlockchainService()
logger.info("🚀 Blockchain service initialized for SKALE Europa DeFi Hub Mainnet")
