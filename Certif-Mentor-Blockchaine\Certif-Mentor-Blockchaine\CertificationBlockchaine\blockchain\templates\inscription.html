﻿<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Inscription | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
  <div class="bg-white p-8 rounded-2xl shadow-md w-full max-w-md">
    <h1 class="text-2xl font-bold text-center text-gray-800 mb-6">C<PERSON>er un compte</h1>
    
    {% if messages %}
      {% for message in messages %}
        <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-3 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <span class="text-red-700 text-sm font-medium">{{ message }}</span>
        </div>
      {% endfor %}
    {% endif %}

    <form method="post" class="space-y-4">
      {% csrf_token %}

      <div>
        <label for="nom" class="block text-sm font-medium text-gray-700">Nom</label>
        <input type="text" id="nom" name="nom" value="{{ form_data.nom }}" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <div>
        <label for="prenom" class="block text-sm font-medium text-gray-700">Prénom</label>
        <input type="text" id="prenom" name="prenom" value="{{ form_data.prenom }}" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700">Adresse e-mail</label>
        <input type="email" id="email" name="email" value="{{ form_data.email }}" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <div>
        <label for="mot_de_passe" class="block text-sm font-medium text-gray-700">Mot de passe</label>
        <input type="password" id="mot_de_passe" name="mot_de_passe" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <div>
        <label for="confirmer_mot_de_passe" class="block text-sm font-medium text-gray-700">Confirmer le mot de passe</label>
        <input type="password" id="confirmer_mot_de_passe" name="confirmer_mot_de_passe" required class="mt-1 w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <button type="submit" class="w-full bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition">
        S'inscrire
      </button>
    </form>

    <p class="text-center text-sm text-gray-600 mt-4">
      Vous avez déjà un compte ? <a href="{% url 'connexion' %}" class="text-blue-600 hover:underline">Connectez-vous</a>
    </p>
  </div>
</body>
</html>
