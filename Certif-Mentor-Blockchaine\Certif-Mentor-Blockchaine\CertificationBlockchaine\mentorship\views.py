from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import datetime, timedelta, date
import calendar
import json
from .models import Meeting

@login_required
def calendar_view(request):
    """Display calendar with scheduled meetings for the logged-in user"""

    # Get current date or date from URL parameter
    today = timezone.now().date()
    year = int(request.GET.get('year', today.year))
    month = int(request.GET.get('month', today.month))

    # Create a date object for the requested month
    current_date = date(year, month, 1)

    # Get the user's meetings for the current month
    user_meetings = Meeting.objects.filter(
        student=request.user,
        date__year=year,
        date__month=month
    ).select_related('student')

    # Create sample meetings for demonstration (since we don't have real data yet)
    sample_meetings = create_sample_meetings(request.user, year, month)

    # Combine real meetings with sample meetings
    all_meetings = list(user_meetings) + sample_meetings

    # Group meetings by date
    meetings_by_date = {}
    for meeting in all_meetings:
        meeting_date = meeting.date
        if meeting_date not in meetings_by_date:
            meetings_by_date[meeting_date] = []
        meetings_by_date[meeting_date].append(meeting)

    # Generate calendar data
    cal = calendar.monthcalendar(year, month)

    # Calculate previous and next month
    if month == 1:
        prev_month = 12
        prev_year = year - 1
    else:
        prev_month = month - 1
        prev_year = year

    if month == 12:
        next_month = 1
        next_year = year + 1
    else:
        next_month = month + 1
        next_year = year

    # Month names in French
    month_names = [
        '', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ]

    context = {
        'calendar_weeks': cal,
        'current_date': current_date,
        'current_month': month_names[month],
        'current_year': year,
        'today': today,
        'meetings_by_date': meetings_by_date,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'meetings_json': json.dumps([
            {
                'id': meeting.id if hasattr(meeting, 'id') else 0,
                'title': meeting.title,
                'mentor_name': meeting.mentor_name,
                'date': meeting.date.isoformat(),
                'start_time': meeting.start_time.strftime('%H:%M'),
                'end_time': meeting.end_time.strftime('%H:%M'),
                'status': meeting.status,
                'meeting_type': meeting.meeting_type
            }
            for meeting in all_meetings
        ], default=str)
    }

    return render(request, 'mentorship/calendar.html', context)

def create_sample_meetings(user, year, month):
    """Create sample meetings for demonstration purposes"""

    # Mentor data (same as in blockchain/views.py)
    mentor_names = {
        1: 'Sophie Dubois', 2: 'Karim Benali', 3: 'Jean Moreau', 4: 'Marie Lefebvre', 5: 'Ahmed Hassan',
        6: 'Claire Martin', 7: 'Lucas Petit', 8: 'Fatima Alaoui', 9: 'Pierre Rousseau', 10: 'Nadia Kone'
    }

    sample_meetings = []

    # Create some sample meetings for the current month
    current_date = date(year, month, 1)

    # Sample meeting 1: Next week
    if month == timezone.now().month and year == timezone.now().year:
        next_week = timezone.now().date() + timedelta(days=7)
        if next_week.month == month:
            meeting1 = type('Meeting', (), {
                'id': 'sample_1',
                'title': 'Session Smart Contracts',
                'mentor_name': mentor_names[1],
                'mentor_id': 1,
                'date': next_week,
                'start_time': datetime.strptime('14:00', '%H:%M').time(),
                'end_time': datetime.strptime('15:00', '%H:%M').time(),
                'status': 'scheduled',
                'meeting_type': 'mentoring',
                'description': 'Révision des concepts de base des smart contracts'
            })()
            sample_meetings.append(meeting1)

    # Sample meeting 2: In 3 days
    if month == timezone.now().month and year == timezone.now().year:
        in_three_days = timezone.now().date() + timedelta(days=3)
        if in_three_days.month == month:
            meeting2 = type('Meeting', (), {
                'id': 'sample_2',
                'title': 'Aide Projet DeFi',
                'mentor_name': mentor_names[4],
                'mentor_id': 4,
                'date': in_three_days,
                'start_time': datetime.strptime('10:30', '%H:%M').time(),
                'end_time': datetime.strptime('11:30', '%H:%M').time(),
                'status': 'scheduled',
                'meeting_type': 'project_help',
                'description': 'Assistance pour le développement d\'un protocole DeFi'
            })()
            sample_meetings.append(meeting2)

    # Sample meeting 3: Next Friday
    if month == timezone.now().month and year == timezone.now().year:
        today = timezone.now().date()
        days_until_friday = (4 - today.weekday()) % 7
        if days_until_friday == 0:  # If today is Friday, get next Friday
            days_until_friday = 7
        next_friday = today + timedelta(days=days_until_friday)
        if next_friday.month == month:
            meeting3 = type('Meeting', (), {
                'id': 'sample_3',
                'title': 'Révision Cryptographie',
                'mentor_name': mentor_names[5],
                'mentor_id': 5,
                'date': next_friday,
                'start_time': datetime.strptime('16:00', '%H:%M').time(),
                'end_time': datetime.strptime('17:00', '%H:%M').time(),
                'status': 'scheduled',
                'meeting_type': 'review',
                'description': 'Révision des algorithmes cryptographiques'
            })()
            sample_meetings.append(meeting3)

    return sample_meetings
