from django.shortcuts import render, redirect
from django.contrib.auth.models import User
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.http import JsonResponse
from datetime import datetime
import random
import json
import os
import hashlib

# Import blockchain service with error handling
try:
    from .blockchain_service import blockchain_service
except ImportError as e:
    print(f"Warning: Could not import blockchain service: {e}")
    # Create a fallback blockchain service
    class FallbackBlockchainService:
        def register_certificate(self, certificate_data, file_content):
            # Generate a fallback certificate ID
            file_hash = hashlib.sha256(file_content).hexdigest()
            fallback_id = hashlib.sha256(
                f"{certificate_data.get('name', '')}{certificate_data.get('issuer', '')}{file_hash}".encode()
            ).hexdigest()

            return {
                'success': True,
                'certificate_id': fallback_id,
                'transaction_hash': f"fallback_{fallback_id[:16]}",
                'block_number': 'N/A (fallback mode)',
                'gas_used': 0,
                'message': 'Certificate registered in fallback mode (blockchain service not available)'
            }

        def verify_certificate_by_id(self, certificate_id):
            return {
                'success': True,
                'exists': False,
                'message': 'Verification not available in fallback mode'
            }

        def verify_certificate_by_hash(self, file_hash):
            return {
                'success': True,
                'exists': False,
                'message': 'Verification not available in fallback mode'
            }

    blockchain_service = FallbackBlockchainService()

def homepage(request):
    # Centralized image mapping for consistency across all pages
    mentor_images = {
        1: 'https://i.pravatar.cc/200?img=10', 2: 'https://i.pravatar.cc/200?img=15', 3: 'https://i.pravatar.cc/200?img=21',
        4: 'https://i.pravatar.cc/200?img=25', 5: 'https://i.pravatar.cc/200?img=30', 6: 'https://i.pravatar.cc/200?img=35',
        7: 'https://i.pravatar.cc/200?img=40', 8: 'https://i.pravatar.cc/200?img=45', 9: 'https://i.pravatar.cc/200?img=50',
        10: 'https://i.pravatar.cc/200?img=55', 11: 'https://i.pravatar.cc/200?img=60', 12: 'https://i.pravatar.cc/200?img=65',
        13: 'https://i.pravatar.cc/200?img=70', 14: 'https://i.pravatar.cc/200?img=75', 15: 'https://i.pravatar.cc/200?img=80',
        16: 'https://i.pravatar.cc/200?img=85', 17: 'https://i.pravatar.cc/200?img=90', 18: 'https://i.pravatar.cc/200?img=95',
        19: 'https://i.pravatar.cc/200?img=12', 20: 'https://i.pravatar.cc/200?img=18', 21: 'https://i.pravatar.cc/200?img=22',
        22: 'https://i.pravatar.cc/200?img=28', 23: 'https://i.pravatar.cc/200?img=33', 24: 'https://i.pravatar.cc/200?img=38',
        25: 'https://i.pravatar.cc/200?img=42'
    }

    # Sample mentor data for homepage display
    mentors = [
        {'id': 1, 'name': 'Sophie Dubois', 'image': mentor_images[1], 'description': 'Blockchain, Smart Contracts, Solidity'},
        {'id': 2, 'name': 'Karim Benali', 'image': mentor_images[2], 'description': 'Développement Web3, IPFS, NFT'},
        {'id': 3, 'name': 'Jean Moreau', 'image': mentor_images[3], 'description': 'Déploiement Ethereum, Truffle, Hardhat'},
        {'id': 4, 'name': 'Marie Lefebvre', 'image': mentor_images[4], 'description': 'DeFi, Yield Farming, Liquidity Pools'},
        {'id': 5, 'name': 'Ahmed Hassan', 'image': mentor_images[5], 'description': 'Cryptographie, Sécurité Blockchain, Audit'},
        {'id': 6, 'name': 'Claire Martin', 'image': mentor_images[6], 'description': 'Tokenomics, ICO, Governance'},
        {'id': 7, 'name': 'Lucas Petit', 'image': mentor_images[7], 'description': 'Layer 2, Polygon, Optimism'},
        {'id': 8, 'name': 'Fatima Alaoui', 'image': mentor_images[8], 'description': 'Cross-chain, Bridges, Interoperability'},
        {'id': 9, 'name': 'Pierre Rousseau', 'image': mentor_images[9], 'description': 'Mining, Consensus, Proof of Stake'},
        {'id': 10, 'name': 'Nadia Kone', 'image': mentor_images[10], 'description': 'Blockchain Analytics, On-chain Data'},
        {'id': 11, 'name': 'Thomas Bernard', 'image': mentor_images[11], 'description': 'Metaverse, Gaming, Virtual Assets'},
        {'id': 12, 'name': 'Leila Mansouri', 'image': mentor_images[12], 'description': 'Regulatory Compliance, Legal Tech'},
        {'id': 13, 'name': 'Antoine Girard', 'image': mentor_images[13], 'description': 'Enterprise Blockchain, Hyperledger'},
        {'id': 14, 'name': 'Yasmine Benali', 'image': mentor_images[14], 'description': 'Bitcoin, Trading, Analyse Technique'},
        {'id': 15, 'name': 'David Chen', 'image': mentor_images[15], 'description': 'Web3, dApps, React Integration'},
        {'id': 16, 'name': 'Elena Rodriguez', 'image': mentor_images[16], 'description': 'Cryptographie, Sécurité, Zero-Knowledge'},
        {'id': 17, 'name': 'Marcus Johnson', 'image': mentor_images[17], 'description': 'Layer 2, Arbitrum, Scaling Solutions'},
        {'id': 18, 'name': 'Amina Ouali', 'image': mentor_images[18], 'description': 'Governance, DAO, Tokenomics'},
        {'id': 19, 'name': 'Olivier Durand', 'image': mentor_images[19], 'description': 'Mining, Proof of Work, Hardware'},
        {'id': 20, 'name': 'Samira Khalil', 'image': mentor_images[20], 'description': 'NFT, Marketplace, OpenSea'},
        {'id': 21, 'name': 'Roberto Silva', 'image': mentor_images[21], 'description': 'Solidity, Testing, Hardhat'},
        {'id': 22, 'name': 'Ines Moreau', 'image': mentor_images[22], 'description': 'DeFi, Uniswap, Liquidity Mining'},
        {'id': 23, 'name': 'Alex Petrov', 'image': mentor_images[23], 'description': 'Web3, MetaMask, Wallet Integration'},
        {'id': 24, 'name': 'Zara Ahmed', 'image': mentor_images[24], 'description': 'Cryptographie, Privacy, Monero'},
        {'id': 25, 'name': 'Vincent Lambert', 'image': mentor_images[25], 'description': 'Trading, Technical Analysis, Portfolio'},
    ]
    return render(request, 'homepage.html', {
        'mentors': mentors,
        'mentors_json': json.dumps(mentors)
    })

def inscription(request):
    # Initialize form data dictionary
    form_data = {
        'nom': '',
        'prenom': '',
        'email': ''
    }

    if request.method == 'POST':
        nom = request.POST.get('nom')
        prenom = request.POST.get('prenom')
        email = request.POST.get('email')
        mot_de_passe = request.POST.get('mot_de_passe')
        confirmer = request.POST.get('confirmer_mot_de_passe')

        # Preserve form data for re-rendering
        form_data = {
            'nom': nom,
            'prenom': prenom,
            'email': email
        }

        if mot_de_passe != confirmer:
            messages.error(request, "Les mots de passe ne correspondent pas.")
            return render(request, 'inscription.html', {'form_data': form_data})

        if User.objects.filter(username=email).exists():
            messages.error(request, "Cet e-mail est déjà utilisé.")
            return render(request, 'inscription.html', {'form_data': form_data})

        user = User.objects.create_user(
            username=email,
            email=email,
            password=mot_de_passe,
            first_name=prenom,
            last_name=nom
        )
        user.save()

        # Auto-login the user after successful registration
        login(request, user)
        return redirect('homepage')

    return render(request, 'inscription.html', {'form_data': form_data})

def connexion(request):
    # Initialize form data
    form_data = {'email': ''}

    if request.method == 'POST':
        email = request.POST.get('email')
        mot_de_passe = request.POST.get('mot_de_passe')

        # Preserve email for re-rendering
        form_data = {'email': email}

        user = authenticate(request, username=email, password=mot_de_passe)
        if user is not None:
            login(request, user)
            return redirect('/')  # redirige vers la page d'accueil ou tableau de bord
        else:
            messages.error(request, "Email ou mot de passe incorrect.")
            return render(request, 'connexion.html', {'form_data': form_data})

    return render(request, 'connexion.html', {'form_data': form_data})

def deconnexion(request):
    logout(request)
    return redirect('homepage')

def filter_mentors(request):
    return render(request, 'filter.html')

def mentor_detail(request, mentor_id):
    # Centralized image mapping for consistency across all pages
    mentor_images = {
        1: 'https://i.pravatar.cc/200?img=10', 2: 'https://i.pravatar.cc/200?img=15', 3: 'https://i.pravatar.cc/200?img=21',
        4: 'https://i.pravatar.cc/200?img=25', 5: 'https://i.pravatar.cc/200?img=30', 6: 'https://i.pravatar.cc/200?img=35',
        7: 'https://i.pravatar.cc/200?img=40', 8: 'https://i.pravatar.cc/200?img=45', 9: 'https://i.pravatar.cc/200?img=50',
        10: 'https://i.pravatar.cc/200?img=55', 11: 'https://i.pravatar.cc/200?img=60', 12: 'https://i.pravatar.cc/200?img=65',
        13: 'https://i.pravatar.cc/200?img=70', 14: 'https://i.pravatar.cc/200?img=75', 15: 'https://i.pravatar.cc/200?img=80',
        16: 'https://i.pravatar.cc/200?img=85', 17: 'https://i.pravatar.cc/200?img=90', 18: 'https://i.pravatar.cc/200?img=95',
        19: 'https://i.pravatar.cc/200?img=12', 20: 'https://i.pravatar.cc/200?img=18', 21: 'https://i.pravatar.cc/200?img=22',
        22: 'https://i.pravatar.cc/200?img=28', 23: 'https://i.pravatar.cc/200?img=33', 24: 'https://i.pravatar.cc/200?img=38',
        25: 'https://i.pravatar.cc/200?img=42'
    }

    # Sample mentor data - in a real app, this would come from a database
    mentors_data = {
        1: {
            'id': 1,
            'name': 'Sophie Dubois',
            'image': mentor_images[1],
            'title': 'Expert en Smart Contracts & Solidity',
            'rating': 5,
            'reviews_count': 47,
            'price': 120,
            'description': 'Développeuse blockchain avec plus de 8 ans d\'expérience dans l\'écosystème Ethereum. Spécialisée dans le développement de smart contracts sécurisés et l\'audit de code. J\'ai travaillé sur des projets DeFi majeurs et formé plus de 200 développeurs.',
            'specializations': ['Smart Contracts', 'Solidity', 'Ethereum', 'Audit de sécurité'],
            'experience': '8 ans',
            'level': 'expert',
            'languages': 'Français, Anglais',
            'availability_text': 'Disponible immédiatement',
            'skills': [
                {'name': 'Solidity', 'level': 95},
                {'name': 'Smart Contracts', 'level': 98},
                {'name': 'Ethereum', 'level': 90},
                {'name': 'Web3.js', 'level': 85},
                {'name': 'Hardhat', 'level': 88}
            ],
            'certifications': [
                {'name': 'Certified Ethereum Developer', 'issuer': 'Ethereum Foundation', 'year': '2021'},
                {'name': 'Blockchain Security Specialist', 'issuer': 'ConsenSys', 'year': '2022'},
                {'name': 'Smart Contract Auditor', 'issuer': 'OpenZeppelin', 'year': '2023'}
            ],
            'reviews': [
                {'student_name': 'Marc L.', 'rating': 5, 'date': '15 Jan 2025', 'comment': 'Excellente formatrice ! Sophie explique les concepts complexes de manière très claire.'},
                {'student_name': 'Julie M.', 'rating': 5, 'date': '10 Jan 2025', 'comment': 'Grâce à Sophie, j\'ai pu développer mon premier smart contract en une semaine.'},
                {'student_name': 'Ahmed K.', 'rating': 4, 'date': '5 Jan 2025', 'comment': 'Très professionnelle et patiente. Recommande fortement !'}
            ]
        },
        2: {
            'id': 2,
            'name': 'Karim Benali',
            'image': mentor_images[2],
            'title': 'Spécialiste Web3 & NFT',
            'rating': 4,
            'reviews_count': 32,
            'price': 85,
            'description': 'Développeur full-stack spécialisé dans les applications Web3 et les NFT. Passionné par l\'innovation blockchain et l\'art numérique. J\'aide les créateurs à lancer leurs projets NFT et les développeurs à maîtriser les technologies Web3.',
            'specializations': ['Web3', 'NFT', 'IPFS', 'React'],
            'experience': '4 ans',
            'level': 'avance',
            'languages': 'Français, Arabe',
            'availability_text': 'Disponible cette semaine',
            'skills': [
                {'name': 'Web3.js', 'level': 90},
                {'name': 'NFT Development', 'level': 85},
                {'name': 'IPFS', 'level': 80},
                {'name': 'React', 'level': 88},
                {'name': 'MetaMask Integration', 'level': 92}
            ],
            'certifications': [
                {'name': 'Web3 Developer', 'issuer': 'Alchemy University', 'year': '2022'},
                {'name': 'NFT Specialist', 'issuer': 'OpenSea', 'year': '2023'}
            ],
            'reviews': [
                {'student_name': 'Sarah B.', 'rating': 4, 'date': '12 Jan 2025', 'comment': 'Karim m\'a aidé à comprendre les NFT et à créer ma première collection.'},
                {'student_name': 'Thomas R.', 'rating': 5, 'date': '8 Jan 2025', 'comment': 'Excellent mentor pour apprendre Web3 !'}
            ]
        }
    }

    # Generate data for mentors 3-25 with realistic information
    if mentor_id not in mentors_data and 3 <= mentor_id <= 25:
        mentor_names = {
            3: 'Jean Moreau', 4: 'Marie Lefebvre', 5: 'Ahmed Hassan', 6: 'Claire Martin',
            7: 'Lucas Petit', 8: 'Fatima Alaoui', 9: 'Pierre Rousseau', 10: 'Nadia Kone',
            11: 'Thomas Bernard', 12: 'Leila Mansouri', 13: 'Antoine Girard', 14: 'Yasmine Benali',
            15: 'David Chen', 16: 'Elena Rodriguez', 17: 'Marcus Johnson', 18: 'Amina Ouali',
            19: 'Olivier Durand', 20: 'Samira Khalil', 21: 'Roberto Silva', 22: 'Ines Moreau',
            23: 'Alex Petrov', 24: 'Zara Ahmed', 25: 'Vincent Lambert'
        }

        mentor_specializations = {
            3: ['Ethereum', 'Truffle', 'Hardhat'], 4: ['DeFi', 'Yield Farming'], 5: ['Cryptographie', 'Sécurité'],
            6: ['Governance', 'Tokenomics'], 7: ['Layer 2', 'Polygon'], 8: ['Cross-chain', 'Bridges'],
            9: ['Mining', 'Consensus'], 10: ['Analytics', 'Data'], 11: ['NFT', 'Gaming'],
            12: ['Legal', 'Compliance'], 13: ['Enterprise', 'Hyperledger'], 14: ['Bitcoin', 'Trading'],
            15: ['Web3', 'React'], 16: ['Cryptographie', 'Zero-Knowledge'], 17: ['Layer 2', 'Arbitrum'],
            18: ['DAO', 'Governance'], 19: ['Mining', 'Hardware'], 20: ['NFT', 'Marketplace'],
            21: ['Solidity', 'Testing'], 22: ['DeFi', 'Uniswap'], 23: ['Web3', 'MetaMask'],
            24: ['Cryptographie', 'Privacy'], 25: ['Trading', 'Analysis']
        }

        import random
        ratings = [3, 4, 4, 4, 5, 5]
        prices = [55, 60, 75, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 140, 150, 155, 160, 170, 180, 190, 200]

        mentors_data[mentor_id] = {
            'id': mentor_id,
            'name': mentor_names[mentor_id],
            'image': mentor_images[mentor_id],
            'title': f'Spécialiste {", ".join(mentor_specializations[mentor_id][:2])}',
            'rating': random.choice(ratings),
            'reviews_count': random.randint(15, 50),
            'price': random.choice(prices),
            'description': f'Expert blockchain spécialisé en {", ".join(mentor_specializations[mentor_id])}. Avec plusieurs années d\'expérience dans l\'industrie, je forme des développeurs et aide les entreprises à adopter la technologie blockchain.',
            'specializations': mentor_specializations[mentor_id],
            'experience': random.choice(['2 ans', '3 ans', '4 ans', '5 ans', '6 ans', '8 ans', '10 ans']),
            'level': random.choice(['intermediaire', 'avance', 'expert']),
            'languages': random.choice(['Français', 'Français, Anglais', 'Français, Espagnol', 'Anglais']),
            'availability_text': random.choice(['Disponible immédiatement', 'Disponible cette semaine', 'Disponible ce mois']),
            'skills': [
                {'name': mentor_specializations[mentor_id][0], 'level': random.randint(80, 95)},
                {'name': mentor_specializations[mentor_id][1] if len(mentor_specializations[mentor_id]) > 1 else 'Blockchain', 'level': random.randint(75, 90)},
                {'name': 'Ethereum', 'level': random.randint(70, 85)},
                {'name': 'Web3', 'level': random.randint(65, 80)}
            ],
            'certifications': [
                {'name': f'Certified {mentor_specializations[mentor_id][0]} Developer', 'issuer': 'Blockchain Institute', 'year': '2022'},
                {'name': 'Blockchain Professional', 'issuer': 'Tech Academy', 'year': '2023'}
            ],
            'reviews': [
                {'student_name': 'Student A.', 'rating': random.choice([4, 5]), 'date': '10 Jan 2025', 'comment': 'Excellent mentor, très professionnel !'},
                {'student_name': 'Student B.', 'rating': random.choice([4, 5]), 'date': '5 Jan 2025', 'comment': 'J\'ai beaucoup appris, je recommande !'}
            ]
        }

    mentor = mentors_data.get(mentor_id)
    if not mentor:
        # In a real app, you'd return a 404 page
        return render(request, 'homepage.html')

    return render(request, 'mentor_detail.html', {'mentor': mentor})

def register_certificate(request):
    """
    View for blockchain certificate registration page.
    This page allows users to register their existing certificates on the blockchain.
    """
    # blockchain_service is already imported at the top

    if request.method == 'POST':
        try:
            # Get form data
            certificate_name = request.POST.get('certificate_name')
            issuer = request.POST.get('issuer')
            issue_date = request.POST.get('issue_date')
            certificate_number = request.POST.get('certificate_number', '')
            description = request.POST.get('description', '')

            # Get uploaded file
            certificate_file = request.FILES.get('certificate_file')
            if not certificate_file:
                return JsonResponse({
                    'success': False,
                    'error': "Veuillez télécharger un fichier de certificat."
                })

            # Read file content and calculate hash
            file_content = certificate_file.read()

            file_hash = hashlib.sha256(file_content).hexdigest()

            # Parse issue date
            try:
                issue_date_obj = datetime.strptime(issue_date, '%Y-%m-%d')
            except:
                issue_date_obj = datetime.now()

            # Prepare certificate data
            certificate_data = {
                'name': certificate_name,
                'issuer': issuer,
                'recipient': getattr(request, 'user', None) and request.user.is_authenticated and request.user.username or 'Anonymous',
                'issue_date': issue_date_obj.timestamp(),
                'certificate_number': certificate_number,
                'description': description
            }

            # Register certificate on real blockchain
            result = blockchain_service.register_certificate(certificate_data, file_content)

            if result['success']:
                # Success - return JSON response for AJAX
                return JsonResponse({
                    'success': True,
                    'certificate_id': result['certificate_id'],
                    'transaction_hash': result['transaction_hash'],
                    'file_hash': file_hash,
                    'gas_used': result.get('gas_used', 0),
                    'explorer_url': f"https://sepolia.etherscan.io/tx/{result['transaction_hash']}"
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': f"Erreur blockchain: {result.get('error', 'Erreur inconnue')}"
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f"Erreur lors de l'enregistrement: {str(e)}"
            })

    return render(request, 'register_certificate.html')

def test_certificate(request):
    """
    Simple test view for certificate registration using real blockchain
    """
    # blockchain_service is already imported at the top

    if request.method == 'POST':
        try:
            # Get form data
            certificate_name = request.POST.get('certificate_name')
            issuer = request.POST.get('issuer')
            issue_date = request.POST.get('issue_date')
            description = request.POST.get('description', '')

            # Get uploaded file
            certificate_file = request.FILES.get('certificate_file')
            if not certificate_file:
                return JsonResponse({
                    'success': False,
                    'error': "Veuillez télécharger un fichier de certificat."
                })

            # Read file content and calculate hash
            file_content = certificate_file.read()
            file_hash = hashlib.sha256(file_content).hexdigest()

            # Parse issue date
            try:
                issue_date_obj = datetime.strptime(issue_date, '%Y-%m-%d')
            except:
                issue_date_obj = datetime.now()

            # Prepare certificate data for blockchain registration
            certificate_data = {
                'name': certificate_name,
                'issuer': issuer,
                'recipient': getattr(request, 'user', None) and request.user.is_authenticated and request.user.username or 'Test User',
                'issue_date': int(issue_date_obj.timestamp()),
                'certificate_number': f'TEST-{file_hash[:8].upper()}',
                'description': description
            }

            # Register certificate on real blockchain
            result = blockchain_service.register_certificate(certificate_data, file_content)

            if result['success']:
                return JsonResponse({
                    'success': True,
                    'certificate_id': result['certificate_id'],
                    'transaction_hash': result['transaction_hash'],
                    'file_hash': file_hash,
                    'gas_used': result.get('gas_used', 0),
                    'explorer_url': f"https://sepolia.etherscan.io/tx/{result['transaction_hash']}",
                    'message': 'Certificat enregistré avec succès sur la blockchain Ethereum Sepolia'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': f"Erreur blockchain: {result.get('error', 'Erreur inconnue')}"
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f"Erreur lors de l'enregistrement: {str(e)}"
            })

    return render(request, 'register_certificate_simple.html')

def verify_certificate(request):
    """
    Public certificate verification page
    Allows third parties to verify certificates by ID, file hash, or QR code
    """
    # Import required modules at the top
    import hashlib

    if request.method == 'POST':
        verification_type = request.POST.get('verification_type')

        try:
            if verification_type == 'certificate_id':
                certificate_id = request.POST.get('certificate_id', '').strip()
                if not certificate_id:
                    return JsonResponse({
                        'success': False,
                        'error': 'Veuillez entrer un ID de certificat valide.'
                    })

                # Verify by certificate ID
                result = verify_certificate_by_id(certificate_id)

            elif verification_type == 'file_hash':
                uploaded_file = request.FILES.get('certificate_file')
                if not uploaded_file:
                    return JsonResponse({
                        'success': False,
                        'error': 'Veuillez télécharger le fichier du certificat.'
                    })

                # Calculate file hash and verify
                file_content = uploaded_file.read()
                file_hash = hashlib.sha256(file_content).hexdigest()
                result = verify_certificate_by_hash(file_hash)

            else:
                return JsonResponse({
                    'success': False,
                    'error': 'Type de vérification non valide.'
                })

            return JsonResponse(result)

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur lors de la vérification: {str(e)}'
            })

    return render(request, 'verify_certificate.html')

def verify_certificate_by_id(certificate_id):
    """
    Verify certificate by its unique ID
    In a real implementation, this would query the blockchain
    """
    # Use real blockchain verification
    # blockchain_service is already imported at the top

    try:
        # Call the real blockchain service
        result = blockchain_service.verify_certificate_by_id(certificate_id)

        if result['success'] and result.get('exists', False):
            return {
                'success': True,
                'verified': True,
                'certificate': {
                    'id': certificate_id,
                    'name': result.get('name', ''),
                    'issuer': result.get('issuer', ''),
                    'recipient': result.get('recipient', ''),
                    'issue_date': result.get('issue_date', ''),
                    'certificate_number': result.get('certificate_number', ''),
                    'description': result.get('description', ''),
                    'registration_date': result.get('registration_date', ''),
                    'registered_by': result.get('registered_by', ''),
                    'file_hash': result.get('file_hash', ''),
                    'blockchain_network': 'SKALE Calypso NFT Hub',
                    'status': 'Valide et authentique'
                }
            }
        else:
            return {
                'success': True,
                'verified': False,
                'message': 'Certificat non trouvé dans la blockchain'
            }
    except Exception as e:
        return {
            'success': False,
            'verified': False,
            'error': f'Erreur lors de la vérification: {str(e)}'
        }

def verify_certificate_by_hash(file_hash):
    """
    Verify certificate by its file hash using actual blockchain verification
    """
    # blockchain_service is already imported at the top

    try:
        # Validate hash format
        if not file_hash or len(file_hash) != 64:
            return {
                'success': False,
                'error': 'Hash de fichier invalide. Le hash doit être un SHA-256 de 64 caractères.'
            }

        # Use real blockchain verification
        result = blockchain_service.verify_certificate_by_hash(file_hash)

        if result['success']:
            # Certificate found on blockchain
            cert_data = result['certificate']
            return {
                'success': True,
                'verified': True,
                'certificate': {
                    'file_hash': file_hash,
                    'name': cert_data['name'],
                    'issuer': cert_data['issuer'],
                    'recipient': cert_data['recipient'],
                    'issue_date': cert_data['issue_date'],
                    'registration_date': cert_data['registration_date'],
                    'certificate_id': result.get('certificate_id', 'N/A'),
                    'blockchain_network': result.get('network', 'SKALE Europa DeFi Hub'),
                    'status': 'Certificat authentique vérifié sur la blockchain',
                    'certificate_number': cert_data.get('certificate_number', ''),
                    'description': cert_data.get('description', ''),
                    'registered_by': cert_data.get('registered_by', '')
                }
            }
        else:
            # Certificate not found on blockchain
            return {
                'success': False,
                'error': 'Certificat non trouvé sur la blockchain. Le fichier pourrait avoir été modifié ou n\'est pas enregistré.'
            }

    except Exception as e:
        return {
            'success': False,
            'error': f'Erreur lors de la vérification blockchain: {str(e)}'
        }

# API Endpoints for external verification
def api_verify_certificate(request, certificate_id):
    """
    Public API endpoint to verify certificate by ID
    Returns JSON response for external systems
    """
    if request.method == 'GET':
        try:
            result = verify_certificate_by_id(certificate_id)
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    else:
        return JsonResponse({
            'success': False,
            'error': 'Only GET method allowed'
        })

def api_verify_certificate_hash(request):
    """
    Public API endpoint to verify certificate by file hash
    """
    if request.method == 'POST':
        try:
            file_hash = request.POST.get('file_hash')
            if not file_hash:
                return JsonResponse({
                    'success': False,
                    'error': 'file_hash parameter required'
                })

            result = verify_certificate_by_hash(file_hash)
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    else:
        return JsonResponse({
            'success': False,
            'error': 'Only POST method allowed'
        })

def api_certificate_info(request):
    """
    API endpoint to get general information about the certificate system
    """
    return JsonResponse({
        'success': True,
        'system_info': {
            'blockchain_network': 'SKALE Europa DeFi Hub',
            'chain_id': 2046399126,
            'explorer_url': 'https://elated-tan-skat.explorer.mainnet.skalenodes.com/',
            'verification_methods': [
                'certificate_id',
                'file_hash',
                'qr_code'
            ],
            'api_version': '1.0',
            'endpoints': {
                'verify_by_id': '/api/verify/{certificate_id}/',
                'verify_by_hash': '/api/verify-hash/',
                'system_info': '/api/info/'
            }
        }
    })

