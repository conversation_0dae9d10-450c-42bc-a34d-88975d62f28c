<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Filtres | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Toggle Switch Styles */
    input:checked ~ div .dot {
      transform: translateX(100%);
      background-color: #3B82F6;
    }
    input:checked ~ div {
      background-color: #DBEAFE;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen">

  <!-- Barre de navigation -->
  <header class="bg-white shadow-md py-6 px-8 flex justify-between items-center">
    <div class="text-3xl font-bold text-blue-600 cursor-pointer hover:text-blue-700 transition-colors" onclick="window.location.href='/'">CertifMentor</div>
    <div class="flex items-center gap-6">
      {% if user.is_authenticated %}
        <!-- Notification Button -->
        <button id="notificationButton" class="relative flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>
          <span class="hidden md:inline text-lg">Notifications</span>
          <span id="notificationBadge" class="notification-badge">3</span>
        </button>
        <div class="relative">
          <button id="userMenuButton" class="text-gray-700 font-medium text-lg hover:text-blue-600 transition-colors cursor-pointer">
            👤 {{ user.first_name }} {{ user.last_name }}
          </button>
        </div>
      {% else %}
        <a href="{% url 'connexion' %}" class="text-blue-600 hover:underline mr-6 text-lg">Se connecter</a>
        <a href="{% url 'inscription' %}" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
      {% endif %}
    </div>
  </header>

  <!-- Breadcrumb -->
  <div class="bg-white border-b border-gray-200 py-4 px-8">
    <nav class="text-sm text-gray-600">
      <a href="{% url 'homepage' %}" class="hover:text-blue-600">Accueil</a>
      <span class="mx-2">•</span>
      <span class="text-gray-800">Filtres</span>
    </nav>
  </div>

  <!-- Main Content -->
  <main class="py-8 px-8">
    <div class="max-w-6xl mx-auto">

      <!-- Filter Section -->
      <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Rechercher mentors et cours</h1>

        <!-- Top Filter Row -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
          <!-- Expertise Level -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Niveau</label>
            <select id="levelFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Tous</option>
              <option value="debutant">Débutant</option>
              <option value="intermediaire">Intermédiaire</option>
              <option value="avance">Avancé</option>
              <option value="expert">Expert</option>
            </select>
          </div>

          <!-- Experience -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Expérience</label>
            <select id="experienceFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Toutes</option>
              <option value="1-2">1-2 ans</option>
              <option value="3-5">3-5 ans</option>
              <option value="5-10">5-10 ans</option>
              <option value="10+">10+ ans</option>
            </select>
          </div>

          <!-- Rating -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Note</label>
            <select id="ratingFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Toutes</option>
              <option value="5">5 étoiles</option>
              <option value="4">4+ étoiles</option>
              <option value="3">3+ étoiles</option>
            </select>
          </div>

          <!-- Price Range -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Prix/h</label>
            <select id="priceFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Tous</option>
              <option value="0-50">0-50€</option>
              <option value="50-100">50-100€</option>
              <option value="100-200">100-200€</option>
              <option value="200+">200€+</option>
            </select>
          </div>

          <!-- Availability -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Disponibilité</label>
            <select id="availabilityFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Toutes</option>
              <option value="immediate">Immédiate</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>

          <!-- Language -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Langue</label>
            <select id="languageFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="">Toutes</option>
              <option value="fr">Français</option>
              <option value="en">Anglais</option>
              <option value="es">Espagnol</option>
              <option value="de">Allemand</option>
            </select>
          </div>
        </div>

        <!-- Specializations Section -->
        <div class="mb-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Spécialisations</h3>

            <!-- Broad Matches Toggle -->
            <div class="flex items-center space-x-3">
              <label class="flex items-center cursor-pointer">
                <input type="checkbox" id="broadMatches" class="sr-only">
                <div class="relative">
                  <div class="block bg-gray-300 w-14 h-8 rounded-full"></div>
                  <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"></div>
                </div>
                <div class="ml-3 text-gray-700 font-medium">
                  <span class="text-sm">Broad Matches</span>
                  <div class="text-xs text-gray-500">Plus de résultats, moins précis</div>
                </div>
              </label>
            </div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="smart-contracts" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Smart Contracts</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="solidity" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Solidity</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="defi" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">DeFi</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="nft" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">NFT</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="web3" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Web3</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="ethereum" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Ethereum</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="bitcoin" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Bitcoin</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="cryptographie" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Cryptographie</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="trading" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Trading</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="mining" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Mining</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="layer2" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Layer 2</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" value="governance" class="specialization-filter rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="text-sm text-gray-700">Governance</span>
            </label>
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex gap-4">
          <button id="applyFilters" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Appliquer les filtres
          </button>
          <button id="resetFilters" class="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors">
            Réinitialiser
          </button>
        </div>
      </div>

      <!-- Results Section -->
      <div class="bg-white rounded-lg shadow-md p-8">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-bold text-gray-800">Résultats des filtres</h2>
          <span id="resultsCount" class="text-gray-600">25 résultats trouvés</span>
        </div>

        <!-- Results Grid -->
        <div id="mentorResults" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <!-- Mentor cards will be populated by JavaScript -->
        </div>

        <!-- No Results Message -->
        <div id="noResults" class="text-center py-12 text-gray-500 hidden">
          <p>Aucun mentor ne correspond à vos critères de recherche.</p>
          <p class="mt-2">Essayez de modifier vos filtres pour voir plus de résultats.</p>
        </div>
      </div>

    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-white text-center py-6 text-base text-gray-500 mt-12">
    &copy; 2025 CertifMentor. Tous droits réservés.
  </footer>

  <script>
    // Centralized image mapping for consistency across all pages
    const mentorImages = {
      1: 'https://i.pravatar.cc/200?img=10', 2: 'https://i.pravatar.cc/200?img=15', 3: 'https://i.pravatar.cc/200?img=21',
      4: 'https://i.pravatar.cc/200?img=25', 5: 'https://i.pravatar.cc/200?img=30', 6: 'https://i.pravatar.cc/200?img=35',
      7: 'https://i.pravatar.cc/200?img=40', 8: 'https://i.pravatar.cc/200?img=45', 9: 'https://i.pravatar.cc/200?img=50',
      10: 'https://i.pravatar.cc/200?img=55', 11: 'https://i.pravatar.cc/200?img=60', 12: 'https://i.pravatar.cc/200?img=65',
      13: 'https://i.pravatar.cc/200?img=70', 14: 'https://i.pravatar.cc/200?img=75', 15: 'https://i.pravatar.cc/200?img=80',
      16: 'https://i.pravatar.cc/200?img=85', 17: 'https://i.pravatar.cc/200?img=90', 18: 'https://i.pravatar.cc/200?img=95',
      19: 'https://i.pravatar.cc/200?img=12', 20: 'https://i.pravatar.cc/200?img=18', 21: 'https://i.pravatar.cc/200?img=22',
      22: 'https://i.pravatar.cc/200?img=28', 23: 'https://i.pravatar.cc/200?img=33', 24: 'https://i.pravatar.cc/200?img=38',
      25: 'https://i.pravatar.cc/200?img=42'
    };

    // Mentor data with all 25 mentors
    const mentors = [
      {
        id: 1,
        name: "Sophie Dubois",
        image: mentorImages[1],
        specializations: ["smart-contracts", "solidity"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 120,
        availability: "immediate",
        language: "fr",
        description: "Blockchain, Smart Contracts, Solidity"
      },
      {
        id: 2,
        name: "Karim Benali",
        image: mentorImages[2],
        specializations: ["web3", "nft"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 85,
        availability: "week",
        language: "fr",
        description: "Développement Web3, IPFS, NFT"
      },
      {
        id: 3,
        name: "Jean Moreau",
        image: mentorImages[3],
        specializations: ["ethereum"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 150,
        availability: "month",
        language: "fr",
        description: "Déploiement Ethereum, Truffle, Hardhat"
      },
      {
        id: 4,
        name: "Marie Lefebvre",
        image: mentorImages[4],
        specializations: ["defi"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 95,
        availability: "immediate",
        language: "fr",
        description: "DeFi, Yield Farming, Liquidity Pools"
      },
      {
        id: 5,
        name: "Ahmed Hassan",
        image: mentorImages[5],
        specializations: ["cryptographie"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 180,
        availability: "week",
        language: "en",
        description: "Cryptographie, Sécurité Blockchain, Audit"
      },
      {
        id: 6,
        name: "Claire Martin",
        image: mentorImages[6],
        specializations: ["governance"],
        level: "avance",
        experience: "5-10",
        rating: 4,
        price: 110,
        availability: "immediate",
        language: "fr",
        description: "Tokenomics, ICO, Governance"
      },
      {
        id: 7,
        name: "Lucas Petit",
        image: mentorImages[7],
        specializations: ["layer2"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 90,
        availability: "week",
        language: "fr",
        description: "Layer 2, Polygon, Optimism"
      },
      {
        id: 8,
        name: "Fatima Alaoui",
        image: mentorImages[8],
        specializations: ["layer2"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 130,
        availability: "month",
        language: "fr",
        description: "Cross-chain, Bridges, Interoperability"
      },
      {
        id: 9,
        name: "Pierre Rousseau",
        image: mentorImages[9],
        specializations: ["mining"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 160,
        availability: "immediate",
        language: "fr",
        description: "Mining, Consensus, Proof of Stake"
      },
      {
        id: 10,
        name: "Nadia Kone",
        image: mentorImages[10],
        specializations: ["ethereum"],
        level: "intermediaire",
        experience: "3-5",
        rating: 4,
        price: 75,
        availability: "week",
        language: "fr",
        description: "Blockchain Analytics, On-chain Data"
      },
      {
        id: 11,
        name: "Thomas Bernard",
        image: mentorImages[11],
        specializations: ["nft"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 100,
        availability: "immediate",
        language: "fr",
        description: "Metaverse, Gaming, Virtual Assets"
      },
      {
        id: 12,
        name: "Leila Mansouri",
        image: mentorImages[12],
        specializations: ["governance"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 140,
        availability: "week",
        language: "fr",
        description: "Regulatory Compliance, Legal Tech"
      },
      {
        id: 13,
        name: "Antoine Girard",
        image: mentorImages[13],
        specializations: ["ethereum"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 170,
        availability: "month",
        language: "fr",
        description: "Enterprise Blockchain, Hyperledger"
      },
      {
        id: 14,
        name: "Yasmine Benali",
        image: mentorImages[14],
        specializations: ["trading"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 105,
        availability: "immediate",
        language: "fr",
        description: "Bitcoin, Trading, Analyse Technique"
      },
      {
        id: 15,
        name: "David Chen",
        image: mentorImages[15],
        specializations: ["web3"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 85,
        availability: "week",
        language: "en",
        description: "Web3, dApps, React Integration"
      },
      {
        id: 16,
        name: "Elena Rodriguez",
        image: mentorImages[16],
        specializations: ["cryptographie"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 140,
        availability: "month",
        language: "en",
        description: "Cryptographie, Sécurité, Zero-Knowledge"
      },
      {
        id: 17,
        name: "Marcus Johnson",
        image: mentorImages[17],
        specializations: ["layer2"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 135,
        availability: "immediate",
        language: "en",
        description: "Layer 2, Arbitrum, Scaling Solutions"
      },
      {
        id: 18,
        name: "Amina Ouali",
        image: mentorImages[18],
        specializations: ["governance"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 95,
        availability: "week",
        language: "fr",
        description: "Governance, DAO, Tokenomics"
      },
      {
        id: 19,
        name: "Olivier Durand",
        image: mentorImages[19],
        specializations: ["mining"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 155,
        availability: "month",
        language: "fr",
        description: "Mining, Proof of Work, Hardware"
      },
      {
        id: 20,
        name: "Samira Khalil",
        image: mentorImages[20],
        specializations: ["nft"],
        level: "intermediaire",
        experience: "1-3",
        rating: 3,
        price: 60,
        availability: "immediate",
        language: "fr",
        description: "NFT, Marketplace, OpenSea"
      },
      {
        id: 21,
        name: "Roberto Silva",
        image: mentorImages[21],
        specializations: ["solidity"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 110,
        availability: "week",
        language: "en",
        description: "Solidity, Testing, Hardhat"
      },
      {
        id: 22,
        name: "Ines Moreau",
        image: mentorImages[22],
        specializations: ["defi"],
        level: "expert",
        experience: "5-10",
        rating: 5,
        price: 125,
        availability: "month",
        language: "fr",
        description: "DeFi, Uniswap, Liquidity Mining"
      },
      {
        id: 23,
        name: "Alex Petrov",
        image: mentorImages[23],
        specializations: ["web3"],
        level: "intermediaire",
        experience: "1-3",
        rating: 3,
        price: 70,
        availability: "immediate",
        language: "en",
        description: "Web3, MetaMask, Wallet Integration"
      },
      {
        id: 24,
        name: "Zara Ahmed",
        image: mentorImages[24],
        specializations: ["cryptographie"],
        level: "avance",
        experience: "3-5",
        rating: 4,
        price: 115,
        availability: "week",
        language: "en",
        description: "Cryptographie, Privacy, Monero"
      },
      {
        id: 25,
        name: "Vincent Lambert",
        image: mentorImages[25],
        specializations: ["trading"],
        level: "expert",
        experience: "10+",
        rating: 5,
        price: 190,
        availability: "month",
        language: "fr",
        description: "Trading, Technical Analysis, Portfolio"
      }
    ];

    // Course data for filtering
    const courses = [
      {
        id: 'smart-contracts',
        name: 'Smart Contracts',
        type: 'course',
        description: 'Maîtrisez le développement de contrats intelligents sur Ethereum',
        price: 299,
        level: 'intermediaire',
        specializations: ['smart-contracts', 'solidity', 'ethereum']
      },
      {
        id: 'defi',
        name: 'DeFi (Finance Décentralisée)',
        type: 'course',
        description: 'Développez des protocoles DeFi et comprenez l\'écosystème',
        price: 349,
        level: 'avance',
        specializations: ['defi', 'ethereum']
      },
      {
        id: 'nft',
        name: 'NFT & Métavers',
        type: 'course',
        description: 'Créez et déployez des collections NFT innovantes',
        price: 279,
        level: 'intermediaire',
        specializations: ['nft', 'ethereum']
      },
      {
        id: 'blockchain-development',
        name: 'Développement Blockchain',
        type: 'course',
        description: 'Construisez votre propre blockchain from scratch',
        price: 399,
        level: 'expert',
        specializations: ['ethereum', 'cryptographie']
      },
      {
        id: 'cryptography',
        name: 'Cryptographie & Sécurité',
        type: 'course',
        description: 'Maîtrisez les algorithmes cryptographiques et la sécurité',
        price: 329,
        level: 'avance',
        specializations: ['cryptographie']
      },
      {
        id: 'web3-development',
        name: 'Développement Web3',
        type: 'course',
        description: 'Créez des applications décentralisées modernes',
        price: 299,
        level: 'intermediaire',
        specializations: ['web3', 'ethereum']
      }
    ];

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      const allItems = [...mentors.map(m => ({...m, type: 'mentor'})), ...courses];
      displayResults(allItems); // Show all mentors and courses by default
      updateResultsCount(allItems.length);
      setupEventListeners();
    });

    function setupEventListeners() {
      // Apply filters button
      document.getElementById('applyFilters').addEventListener('click', applyFilters);

      // Reset filters button
      document.getElementById('resetFilters').addEventListener('click', resetFilters);

      // Remove real-time filtering - only manual button clicks
    }

    function showInitialMessage() {
      const resultsContainer = document.getElementById('mentorResults');
      const noResultsDiv = document.getElementById('noResults');

      resultsContainer.innerHTML = '';
      noResultsDiv.classList.add('hidden');

      // Show initial message
      resultsContainer.innerHTML = `
        <div class="col-span-full text-center py-12 text-gray-500">
          <p class="text-lg">Utilisez les filtres ci-dessus pour trouver des mentors.</p>
          <p class="mt-2">Cliquez sur "Appliquer les filtres" pour voir les résultats.</p>
        </div>
      `;

      document.getElementById('resultsCount').textContent = '25 mentors disponibles';
    }

    function applyFilters() {
      const filters = getFilterValues();

      // Filter mentors
      const filteredMentors = mentors.filter(mentor => matchesFilters(mentor, filters))
        .map(mentor => ({...mentor, type: 'mentor'}));

      // Filter courses (courses don't have experience, rating, availability, language)
      const filteredCourses = courses.filter(course => matchesCourseFilters(course, filters));

      // Combine results
      const allResults = [...filteredMentors, ...filteredCourses];

      displayResults(allResults);
      updateResultsCount(allResults.length);
    }

    function getFilterValues() {
      return {
        level: document.getElementById('levelFilter').value,
        experience: document.getElementById('experienceFilter').value,
        rating: document.getElementById('ratingFilter').value,
        price: document.getElementById('priceFilter').value,
        availability: document.getElementById('availabilityFilter').value,
        language: document.getElementById('languageFilter').value,
        specializations: Array.from(document.querySelectorAll('.specialization-filter:checked')).map(cb => cb.value),
        broadMatches: document.getElementById('broadMatches').checked
      };
    }

    function matchesFilters(mentor, filters) {
      // Level filter
      if (filters.level && mentor.level !== filters.level) return false;

      // Experience filter
      if (filters.experience && mentor.experience !== filters.experience) return false;

      // Rating filter
      if (filters.rating) {
        const minRating = parseInt(filters.rating);
        if (mentor.rating < minRating) return false;
      }

      // Price filter
      if (filters.price) {
        const priceRange = filters.price;
        if (priceRange === '0-50' && mentor.price > 50) return false;
        if (priceRange === '50-100' && (mentor.price < 50 || mentor.price > 100)) return false;
        if (priceRange === '100-200' && (mentor.price < 100 || mentor.price > 200)) return false;
        if (priceRange === '200+' && mentor.price < 200) return false;
      }

      // Availability filter
      if (filters.availability && mentor.availability !== filters.availability) return false;

      // Language filter
      if (filters.language && mentor.language !== filters.language) return false;

      // Specializations filter
      if (filters.specializations.length > 0) {
        if (filters.broadMatches) {
          // Broad Matches: mentor needs to match ANY selected specialization
          const hasMatchingSpecialization = filters.specializations.some(spec =>
            mentor.specializations.includes(spec)
          );
          if (!hasMatchingSpecialization) return false;
        } else {
          // Strict Matches: mentor needs to match ALL selected specializations
          const hasAllSpecializations = filters.specializations.every(spec =>
            mentor.specializations.includes(spec)
          );
          if (!hasAllSpecializations) return false;
        }
      }

      return true;
    }

    function matchesCourseFilters(course, filters) {
      // Level filter
      if (filters.level && course.level !== filters.level) return false;

      // Price filter (courses use different price ranges)
      if (filters.price) {
        const priceRange = filters.price;
        if (priceRange === '0-50' && course.price > 50) return false;
        if (priceRange === '50-100' && (course.price < 50 || course.price > 100)) return false;
        if (priceRange === '100-200' && (course.price < 100 || course.price > 200)) return false;
        if (priceRange === '200+' && course.price < 200) return false;
      }

      // Specializations filter
      if (filters.specializations.length > 0) {
        if (filters.broadMatches) {
          // Broad Matches: course needs to match ANY selected specialization
          const hasMatchingSpecialization = filters.specializations.some(spec =>
            course.specializations.includes(spec)
          );
          if (!hasMatchingSpecialization) return false;
        } else {
          // Strict Matches: course needs to match ALL selected specializations
          const hasAllSpecializations = filters.specializations.every(spec =>
            course.specializations.includes(spec)
          );
          if (!hasAllSpecializations) return false;
        }
      }

      return true;
    }

    function displayResults(itemsToShow) {
      const resultsContainer = document.getElementById('mentorResults');
      const noResultsDiv = document.getElementById('noResults');

      if (mentorsToShow.length === 0) {
        resultsContainer.innerHTML = '';
        noResultsDiv.classList.remove('hidden');
        return;
      }

      noResultsDiv.classList.add('hidden');

      resultsContainer.innerHTML = itemsToShow.map(item => {
        if (item.type === 'course') {
          // Course card
          return `
            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location.href='/certification/${item.id}/'">
              <div class="w-20 h-20 rounded-full mx-auto mb-4 bg-blue-100 flex items-center justify-center">
                <span class="text-blue-600 text-2xl">📚</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-800 text-center mb-2">${item.name}</h3>
              <p class="text-sm text-gray-600 text-center mb-3">${item.description}</p>
              <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">Cours</span>
                <span>${item.price}€</span>
              </div>
              <div class="text-center">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors" onclick="event.stopPropagation(); window.location.href='/certification/${item.id}/'">
                  Voir le cours
                </button>
              </div>
            </div>
          `;
        } else {
          // Mentor card
          return `
            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location.href='/mentor/${item.id}/'">
              <img src="${item.image}" alt="${item.name}" class="w-20 h-20 rounded-full mx-auto mb-4 mentor-avatar"
                   onerror="this.src='https://ui-avatars.com/api/?name=${encodeURIComponent(item.name)}&size=200&background=e5e7eb&color=6b7280&bold=true'">
              <h3 class="text-lg font-semibold text-gray-800 text-center mb-2">${item.name}</h3>
              <p class="text-sm text-gray-600 text-center mb-3">${item.description}</p>
              <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                <span>⭐ ${item.rating}/5</span>
                <span>${item.price}€/h</span>
              </div>
              <div class="text-center">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="event.stopPropagation(); window.location.href='/mentor/${item.id}/'">
                  Voir le profil
                </button>
              </div>
            </div>
          `;
        }
      }).join('');
    }

    function updateResultsCount(count) {
      document.getElementById('resultsCount').textContent = `${count} résultat${count !== 1 ? 's' : ''} trouvé${count !== 1 ? 's' : ''}`;
    }

    function resetFilters() {
      // Reset all select elements
      document.getElementById('levelFilter').value = '';
      document.getElementById('experienceFilter').value = '';
      document.getElementById('ratingFilter').value = '';
      document.getElementById('priceFilter').value = '';
      document.getElementById('availabilityFilter').value = '';
      document.getElementById('languageFilter').value = '';

      // Reset all checkboxes
      document.querySelectorAll('.specialization-filter').forEach(checkbox => {
        checkbox.checked = false;
      });

      // Reset broad matches toggle
      document.getElementById('broadMatches').checked = false;

      // Show all items (back to default state)
      const allItems = [...mentors.map(m => ({...m, type: 'mentor'})), ...courses];
      displayResults(allItems);
      updateResultsCount(allItems.length);
    }
  </script>

</body>
</html>
