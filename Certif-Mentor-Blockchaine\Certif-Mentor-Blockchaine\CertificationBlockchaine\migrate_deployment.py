#!/usr/bin/env python3
"""
Migration script to help users transition from old deployment scripts to unified_deploy.py
"""
import os
import shutil
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def backup_old_scripts():
    """Backup old deployment scripts to a backup directory"""
    backup_dir = f"deployment_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    old_scripts = [
        'deploy_contract.py',
        'deploy_europa_mainnet.py', 
        'deploy_fresh_contract.py',
        'deploy_new_contract.py',
        'deploy_with_your_wallet.py'
    ]
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        backed_up = []
        
        for script in old_scripts:
            if os.path.exists(script):
                shutil.copy2(script, os.path.join(backup_dir, script))
                backed_up.append(script)
                logger.info(f"✅ Backed up {script}")
        
        if backed_up:
            logger.info(f"📁 Old deployment scripts backed up to: {backup_dir}")
            return backup_dir
        else:
            logger.info("ℹ️ No old deployment scripts found to backup")
            os.rmdir(backup_dir)  # Remove empty backup directory
            return None
            
    except Exception as e:
        logger.error(f"❌ Failed to backup old scripts: {e}")
        return None

def create_migration_guide():
    """Create a migration guide for users"""
    guide_content = """# Deployment Script Migration Guide

## Overview
The multiple deployment scripts have been consolidated into a single `unified_deploy.py` script for better maintainability and consistency.

## Old Scripts → New Unified Script

### deploy_europa_mainnet.py
```bash
# Old way:
python deploy_europa_mainnet.py

# New way:
python unified_deploy.py --type mainnet
```

### deploy_fresh_contract.py
```bash
# Old way:
python deploy_fresh_contract.py

# New way:
python unified_deploy.py --type fresh
```

### deploy_new_contract.py
```bash
# Old way:
python deploy_new_contract.py

# New way:
python unified_deploy.py --type standard
```

### deploy_with_your_wallet.py
```bash
# Old way:
python deploy_with_your_wallet.py

# New way:
python unified_deploy.py --type standard
```

### deploy_contract.py
```bash
# Old way:
python deploy_contract.py

# New way:
python unified_deploy.py --type standard
```

## New Features in Unified Script

1. **Better Error Handling**: Comprehensive error checking and recovery guidance
2. **Standardized Address Updates**: Consistent contract address updates across all files
3. **Environment Validation**: Pre-deployment checks to ensure everything is ready
4. **Flexible Options**: Command-line arguments for different deployment scenarios
5. **Automated Mode**: Option to skip confirmation prompts for CI/CD

## Command Line Options

```bash
# Standard deployment with prompts
python unified_deploy.py

# Fresh contract deployment
python unified_deploy.py --type fresh

# Mainnet deployment with extra warnings
python unified_deploy.py --type mainnet

# Automated deployment (no prompts)
python unified_deploy.py --type auto --no-confirm

# Use custom private key
python unified_deploy.py --private-key YOUR_PRIVATE_KEY

# Verbose logging
python unified_deploy.py --verbose
```

## Benefits of Migration

1. **Single Source of Truth**: One script handles all deployment scenarios
2. **Consistent Behavior**: Standardized error handling and address updates
3. **Better Maintenance**: Easier to update and maintain one script
4. **Enhanced Features**: Better logging, validation, and error recovery
5. **Future-Proof**: Easier to add new features and deployment options

## Migration Steps

1. Backup your old scripts (done automatically by migrate_deployment.py)
2. Test the new unified script in your environment
3. Update any CI/CD pipelines to use the new script
4. Remove old scripts once you're confident in the new one

## Troubleshooting

If you encounter issues with the new script:

1. Check the verbose output: `python unified_deploy.py --verbose`
2. Verify your environment: The script validates prerequisites automatically
3. Compare with old script behavior: Check the backup directory for reference
4. Report issues: Document any problems for further investigation

## Rollback Plan

If you need to rollback to the old scripts:
1. The old scripts are backed up in the `deployment_backup_*` directory
2. Copy them back to the main directory
3. Use them as before while reporting issues with the new script
"""
    
    try:
        with open('DEPLOYMENT_MIGRATION_GUIDE.md', 'w') as f:
            f.write(guide_content)
        logger.info("📖 Migration guide created: DEPLOYMENT_MIGRATION_GUIDE.md")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create migration guide: {e}")
        return False

def main():
    """Main migration function"""
    print("🔄 Deployment Script Migration Tool")
    print("=" * 50)
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # Backup old scripts
    backup_dir = backup_old_scripts()
    
    # Create migration guide
    guide_created = create_migration_guide()
    
    # Summary
    print("\n📋 Migration Summary:")
    if backup_dir:
        print(f"   ✅ Old scripts backed up to: {backup_dir}")
    else:
        print("   ℹ️ No old scripts found to backup")
    
    if guide_created:
        print("   ✅ Migration guide created: DEPLOYMENT_MIGRATION_GUIDE.md")
    else:
        print("   ❌ Failed to create migration guide")
    
    print("\n🚀 Next Steps:")
    print("   1. Review the migration guide: DEPLOYMENT_MIGRATION_GUIDE.md")
    print("   2. Test the new unified deployment script:")
    print("      python unified_deploy.py --type standard")
    print("   3. Update any automation/CI scripts to use unified_deploy.py")
    print("   4. Remove old scripts once you're confident in the new one")
    
    if backup_dir:
        print(f"   5. Old scripts are safely backed up in: {backup_dir}")

if __name__ == "__main__":
    main()
