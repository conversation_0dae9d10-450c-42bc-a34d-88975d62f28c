#!/usr/bin/env python3
"""
Simple test to check if we can read the private key correctly
"""

def test_key():
    print("🔑 SIMPLE KEY TEST")
    print("=" * 30)
    
    # The private key that should be read
    private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    print(f"Key: {private_key_hex}")
    print(f"Length: {len(private_key_hex)} chars")
    
    if len(private_key_hex) == 64:
        print("✅ Key length is correct (64 chars)")
        return True
    else:
        print(f"❌ Key length is wrong (expected 64, got {len(private_key_hex)})")
        return False

if __name__ == "__main__":
    test_key()
    print("Test completed!")
