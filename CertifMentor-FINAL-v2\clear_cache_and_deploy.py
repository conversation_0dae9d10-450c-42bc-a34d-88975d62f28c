#!/usr/bin/env python3
"""
CLEAR ALL CACHES AND DEPLOY FRESH
"""
import os
import shutil
import sys
from pathlib import Path
from web3 import Web3
from solcx import compile_source, set_solc_version, install_solc

def clear_all_caches():
    """Clear all Python and Solidity caches"""
    print("🧹 CLEARING ALL CACHES")
    print("=" * 40)
    
    # 1. Clear Python __pycache__ directories
    print("🗑️ Clearing Python cache...")
    current_dir = Path(".")
    for pycache_dir in current_dir.rglob("__pycache__"):
        try:
            shutil.rmtree(pycache_dir)
            print(f"   ✅ Removed {pycache_dir}")
        except Exception as e:
            print(f"   ⚠️ Could not remove {pycache_dir}: {e}")
    
    # 2. Clear .pyc files
    print("🗑️ Clearing .pyc files...")
    for pyc_file in current_dir.rglob("*.pyc"):
        try:
            pyc_file.unlink()
            print(f"   ✅ Removed {pyc_file}")
        except Exception as e:
            print(f"   ⚠️ Could not remove {pyc_file}: {e}")
    
    # 3. Clear solcx cache (common location)
    print("🗑️ Clearing Solidity cache...")
    try:
        # Common solcx cache locations
        home_dir = Path.home()
        solcx_cache_dirs = [
            home_dir / ".solcx",
            home_dir / ".cache" / "solcx",
            Path(os.environ.get("APPDATA", "")) / "solcx" if os.name == "nt" else None
        ]
        
        for cache_dir in solcx_cache_dirs:
            if cache_dir and cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                    print(f"   ✅ Removed Solidity cache: {cache_dir}")
                except Exception as e:
                    print(f"   ⚠️ Could not remove {cache_dir}: {e}")
    except Exception as e:
        print(f"   ⚠️ Error clearing Solidity cache: {e}")
    
    # 4. Clear pip cache
    print("🗑️ Clearing pip cache...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "cache", "purge"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Pip cache cleared")
        else:
            print(f"   ⚠️ Pip cache clear warning: {result.stderr}")
    except Exception as e:
        print(f"   ⚠️ Could not clear pip cache: {e}")
    
    print("✅ Cache clearing complete!")

def fresh_install_solc():
    """Fresh install of Solidity compiler"""
    print("\n🔧 FRESH SOLIDITY INSTALL")
    print("=" * 40)
    
    try:
        # Install fresh
        print("⬇️ Installing Solidity 0.8.19...")
        install_solc('0.8.19')
        
        # Set version
        set_solc_version('0.8.19')
        print("✅ Solidity compiler ready!")
        
        return True
    except Exception as e:
        print(f"❌ Solidity install failed: {e}")
        return False

def deploy_fresh_contract():
    """Deploy contract with fresh environment"""
    print("\n🚀 FRESH CONTRACT DEPLOYMENT")
    print("=" * 40)
    
    # Simple certificate contract
    CONTRACT_SOURCE = '''
pragma solidity ^0.8.19;

contract CertificateRegistry {
    mapping(string => string) public certificates;
    address public owner;

    constructor() {
        owner = msg.sender;
    }

    function register(string memory id, string memory data) public {
        require(msg.sender == owner);
        certificates[id] = data;
    }

    function get(string memory id) public view returns (string memory) {
        return certificates[id];
    }
}
'''
    
    # Connect to SKALE Europa
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False
    
    print(f"✅ Connected to SKALE Europa")
    print(f"📊 Latest block: {w3.eth.block_number}")
    
    # Your private key
    private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    private_key_bytes = bytes.fromhex(private_key_hex)
    account = w3.eth.account.from_key(private_key_bytes)
    
    print(f"👛 Wallet: {account.address}")
    
    # Get balance
    balance = w3.eth.get_balance(account.address)
    print(f"💰 Balance: {w3.from_wei(balance, 'ether')} sFUEL")
    
    # Compile contract with fresh environment
    print(f"\n🔨 Compiling contract (fresh)...")
    try:
        compiled_sol = compile_source(CONTRACT_SOURCE)
        contract_interface = compiled_sol['<stdin>:CertificateRegistry']
        print(f"✅ Contract compiled successfully!")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Create contract instance
    contract = w3.eth.contract(
        abi=contract_interface['abi'],
        bytecode=contract_interface['bin']
    )
    
    # Get network info
    chain_id = w3.eth.chain_id
    gas_price = w3.eth.gas_price
    nonce = w3.eth.get_transaction_count(account.address)
    
    print(f"\n📋 NETWORK INFO:")
    print(f"🔗 Chain ID: {chain_id}")
    print(f"⛽ Gas Price: {gas_price} wei")
    print(f"🔢 Nonce: {nonce}")
    
    # Use 500K gas (reasonable amount)
    gas_limit = 500000
    print(f"⛽ Gas Limit: {gas_limit:,}")
    
    # Build transaction
    transaction = contract.constructor().build_transaction({
        'chainId': chain_id,
        'gas': gas_limit,
        'gasPrice': gas_price,
        'nonce': nonce,
    })
    
    print(f"💰 Max Cost: {w3.from_wei(gas_limit * gas_price, 'ether')} sFUEL")
    
    # Sign and send
    print(f"\n🔐 Signing transaction...")
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key_bytes)
    
    print(f"📤 Sending transaction...")
    tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
    
    print(f"✅ Transaction sent!")
    print(f"🔗 TX Hash: {tx_hash.hex()}")
    print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
    
    # Wait for confirmation
    print(f"\n⏳ Waiting for confirmation...")
    try:
        receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if receipt.status == 1:
            print(f"\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print(f"📍 Contract Address: {receipt.contractAddress}")
            print(f"⛽ Gas Used: {receipt.gasUsed:,}")
            print(f"💰 Cost: {w3.from_wei(receipt.gasUsed * gas_price, 'ether')} sFUEL")
            
            # Test the contract
            deployed_contract = w3.eth.contract(
                address=receipt.contractAddress,
                abi=contract_interface['abi']
            )
            
            owner = deployed_contract.functions.owner().call()
            print(f"👑 Contract Owner: {owner}")
            
            print(f"\n🎯 SUCCESS! YOUR CERTIFICATE SYSTEM IS LIVE!")
            print(f"📋 Contract Address: {receipt.contractAddress}")
            print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{receipt.contractAddress}")
            
            return True
        else:
            print(f"❌ Transaction failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error waiting for receipt: {e}")
        return False

if __name__ == "__main__":
    print("🧹 CACHE CLEARING AND FRESH DEPLOYMENT")
    print("=" * 60)
    
    # Step 1: Clear all caches
    clear_all_caches()
    
    # Step 2: Fresh Solidity install
    if not fresh_install_solc():
        print("❌ Failed to install Solidity")
        sys.exit(1)
    
    # Step 3: Deploy with fresh environment
    if deploy_fresh_contract():
        print(f"\n🏆 COMPLETE SUCCESS!")
        print(f"🎉 Cache cleared and contract deployed!")
    else:
        print(f"\n❌ Deployment failed after cache clearing")
        sys.exit(1)
