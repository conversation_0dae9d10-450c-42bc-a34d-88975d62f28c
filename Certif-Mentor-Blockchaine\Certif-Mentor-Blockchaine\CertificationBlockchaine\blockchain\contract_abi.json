[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "certificateId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "issuer", "type": "string"}, {"indexed": false, "internalType": "string", "name": "recipient", "type": "string"}, {"indexed": false, "internalType": "address", "name": "registeredBy", "type": "address"}], "name": "CertificateRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "certificateId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "verifiedBy", "type": "address"}], "name": "CertificateVerified", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}], "name": "certificateExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "certificateIds", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "certificates", "outputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "issuer", "type": "string"}, {"internalType": "string", "name": "recipient", "type": "string"}, {"internalType": "uint256", "name": "issueDate", "type": "uint256"}, {"internalType": "string", "name": "certificateNumber", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "fileHash", "type": "string"}, {"internalType": "address", "name": "registeredBy", "type": "address"}, {"internalType": "uint256", "name": "registrationDate", "type": "uint256"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_index", "type": "uint256"}], "name": "getCertificateIdByIndex", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalCertificates", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "hashToCertificateId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}], "name": "logVerification", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_issuer", "type": "string"}, {"internalType": "string", "name": "_recipient", "type": "string"}, {"internalType": "uint256", "name": "_issueDate", "type": "uint256"}, {"internalType": "string", "name": "_certificateNumber", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "string", "name": "_fileHash", "type": "string"}], "name": "registerCertificate", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_certificateId", "type": "bytes32"}], "name": "verifyCertificate", "outputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "issuer", "type": "string"}, {"internalType": "string", "name": "recipient", "type": "string"}, {"internalType": "uint256", "name": "issueDate", "type": "uint256"}, {"internalType": "string", "name": "certificateNumber", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "fileHash", "type": "string"}, {"internalType": "address", "name": "registeredBy", "type": "address"}, {"internalType": "uint256", "name": "registrationDate", "type": "uint256"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_fileHash", "type": "string"}], "name": "verifyCertificateByHash", "outputs": [{"internalType": "bytes32", "name": "certificateId", "type": "bytes32"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "issuer", "type": "string"}, {"internalType": "string", "name": "recipient", "type": "string"}, {"internalType": "uint256", "name": "issueDate", "type": "uint256"}, {"internalType": "string", "name": "certificateNumber", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "registeredBy", "type": "address"}, {"internalType": "uint256", "name": "registrationDate", "type": "uint256"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}]