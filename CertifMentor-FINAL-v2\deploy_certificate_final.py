#!/usr/bin/env python3
"""
FINAL CERTIFICATE DEPLOYMENT SCRIPT - CLEAN VERSION
No folder conflicts, no cache issues!
"""
from web3 import Web3
from solcx import compile_source, set_solc_version

# Set Solc version
set_solc_version('0.8.19')

# Minimal certificate contract - based on successful deployments
CONTRACT_SOURCE = '''
pragma solidity ^0.8.19;

contract CertificateRegistry {
    mapping(string => string) public certificates;
    address public owner;

    constructor() {
        owner = msg.sender;
    }

    function register(string memory id, string memory data) public {
        require(msg.sender == owner);
        certificates[id] = data;
    }

    function get(string memory id) public view returns (string memory) {
        return certificates[id];
    }
}
'''

def deploy_final_contract():
    print("🚀 FINAL CERTIFICATE DEPLOYMENT")
    print("=" * 50)
    
    # Connect to SKALE Europa
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False
    
    print(f"✅ Connected to SKALE Europa")
    print(f"📊 Latest block: {w3.eth.block_number}")
    
    # Your private key (clean, no conflicts!)
    private_key_hex = "dfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    print(f"🔑 Private key length: {len(private_key_hex)} chars")
    
    if len(private_key_hex) != 64:
        print(f"❌ Invalid private key length: {len(private_key_hex)}")
        return False
    
    # Convert to bytes
    private_key_bytes = bytes.fromhex(private_key_hex)
    account = w3.eth.account.from_key(private_key_bytes)
    wallet_address = account.address
    
    print(f"👛 Wallet: {wallet_address}")
    
    # Get balance
    balance = w3.eth.get_balance(wallet_address)
    print(f"💰 Balance: {w3.from_wei(balance, 'ether')} sFUEL")
    
    if balance == 0:
        print("❌ No sFUEL balance! Get some from the faucet first.")
        return False
    
    # Compile contract
    print(f"\n🔨 Compiling contract...")
    compiled_sol = compile_source(CONTRACT_SOURCE)
    contract_interface = compiled_sol['<stdin>:CertificateRegistry']
    
    print(f"✅ Contract compiled successfully!")
    print(f"📝 ABI items: {len(contract_interface['abi'])}")
    print(f"💾 Bytecode length: {len(contract_interface['bin'])} chars")
    
    # Create contract instance
    contract = w3.eth.contract(
        abi=contract_interface['abi'],
        bytecode=contract_interface['bin']
    )
    
    # Get network info
    chain_id = w3.eth.chain_id
    gas_price = w3.eth.gas_price
    nonce = w3.eth.get_transaction_count(wallet_address)
    
    print(f"\n📋 NETWORK INFO:")
    print(f"🔗 Chain ID: {chain_id}")
    print(f"⛽ Gas Price: {gas_price} wei ({w3.from_wei(gas_price, 'gwei')} Gwei)")
    print(f"🔢 Nonce: {nonce}")
    
    # Back to reasonable gas limit - estimation was giving crazy numbers
    gas_limit = 1000000  # 1M gas should be plenty for a simple contract
    print(f"⛽ Gas Limit: {gas_limit:,}")
    
    # Build transaction
    transaction = contract.constructor().build_transaction({
        'chainId': chain_id,
        'gas': gas_limit,
        'gasPrice': gas_price,
        'nonce': nonce,
    })
    
    print(f"💰 Max Cost: {w3.from_wei(gas_limit * gas_price, 'ether')} sFUEL")
    
    # Sign and send
    print(f"\n🔐 Signing transaction...")
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key_bytes)
    
    print(f"📤 Sending transaction...")
    tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
    
    print(f"✅ Transaction sent!")
    print(f"🔗 TX Hash: {tx_hash.hex()}")
    print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
    
    # Wait for confirmation
    print(f"\n⏳ Waiting for confirmation...")
    try:
        receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        if receipt.status == 1:
            print(f"\n🎉 CONTRACT DEPLOYED SUCCESSFULLY!")
            print(f"📍 Contract Address: {receipt.contractAddress}")
            print(f"⛽ Gas Used: {receipt.gasUsed:,}")
            print(f"💰 Cost: {w3.from_wei(receipt.gasUsed * gas_price, 'ether')} sFUEL")
            
            # Test the contract
            deployed_contract = w3.eth.contract(
                address=receipt.contractAddress,
                abi=contract_interface['abi']
            )
            
            owner = deployed_contract.functions.owner().call()
            print(f"👑 Contract Owner: {owner}")
            
            print(f"\n🎯 SUCCESS! YOUR CERTIFICATE SYSTEM IS LIVE!")
            print(f"📋 Contract Address: {receipt.contractAddress}")
            print(f"🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{receipt.contractAddress}")
            
            return receipt.contractAddress
        else:
            print(f"❌ Transaction failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error waiting for receipt: {e}")
        return False

if __name__ == "__main__":
    print("🎉 FINAL DEPLOYMENT - NO MORE CONFLICTS!")
    print("🚀 DAYS OF WORK ABOUT TO PAY OFF!")
    print("=" * 60)
    
    contract_address = deploy_final_contract()
    
    if contract_address:
        print(f"\n🏆 VICTORY! YOUR BLOCKCHAIN CERTIFICATE SYSTEM IS DEPLOYED!")
        print(f"🎯 Contract Address: {contract_address}")
        print(f"🌟 You can now register and verify certificates on the blockchain!")
    else:
        print(f"\n❌ Deployment failed, but we're so close!")
