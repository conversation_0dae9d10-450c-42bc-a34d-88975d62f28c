#!/usr/bin/env python3
"""
Test network/RPC issues that could cause deployment failures
"""
import os
import sys
import django
from web3 import Web3
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_network_congestion():
    """Test 1: Check if network is congested"""
    print("🔍 Test 1: Network Congestion Check")
    print("=" * 50)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    try:
        # Test multiple rapid calls to see response times
        start_times = []
        for i in range(5):
            start = time.time()
            block_num = w3.eth.block_number
            end = time.time()
            response_time = end - start
            start_times.append(response_time)
            print(f"   Call {i+1}: Block {block_num:,} - Response time: {response_time:.3f}s")
            time.sleep(0.5)
        
        avg_time = sum(start_times) / len(start_times)
        print(f"   Average response time: {avg_time:.3f}s")
        
        if avg_time > 5.0:
            print("   ❌ SLOW: Network appears congested")
            return False
        elif avg_time > 2.0:
            print("   ⚠️  MODERATE: Network is somewhat slow")
            return True
        else:
            print("   ✅ FAST: Network response is good")
            return True
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def test_rpc_endpoint():
    """Test 2: Check RPC endpoint stability"""
    print("\n🔍 Test 2: RPC Endpoint Stability")
    print("=" * 50)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # Test basic connection
        if not w3.is_connected():
            print("   ❌ FAILED: Cannot connect to RPC")
            return False
        
        print(f"   ✅ Connected to: {rpc_url}")
        
        # Test multiple endpoint calls
        tests = [
            ("eth_blockNumber", lambda: w3.eth.block_number),
            ("eth_chainId", lambda: w3.eth.chain_id),
            ("eth_gasPrice", lambda: w3.eth.gas_price),
            ("net_version", lambda: w3.net.version),
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                print(f"   ✅ {test_name}: {result}")
            except Exception as e:
                print(f"   ❌ {test_name}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ RPC ERROR: {e}")
        return False

def test_nonce_conflicts():
    """Test 3: Check for nonce conflicts"""
    print("\n🔍 Test 3: Nonce Conflict Check")
    print("=" * 50)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    wallet_address = "******************************************"
    
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # Get current nonce
        nonce = w3.eth.get_transaction_count(wallet_address)
        print(f"   Current nonce: {nonce}")
        
        # Get pending nonce
        pending_nonce = w3.eth.get_transaction_count(wallet_address, 'pending')
        print(f"   Pending nonce: {pending_nonce}")
        
        if nonce != pending_nonce:
            print(f"   ⚠️  WARNING: Nonce mismatch! {pending_nonce - nonce} pending transactions")
            return False
        else:
            print("   ✅ Nonce is clean (no pending transactions)")
            return True
            
    except Exception as e:
        print(f"   ❌ NONCE ERROR: {e}")
        return False

def test_network_requirements():
    """Test 4: Check SKALE-specific network requirements"""
    print("\n🔍 Test 4: SKALE Europa Network Requirements")
    print("=" * 50)
    
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # Check chain ID
        chain_id = w3.eth.chain_id
        expected_chain_id = **********
        print(f"   Chain ID: {chain_id}")
        if chain_id != expected_chain_id:
            print(f"   ❌ WRONG CHAIN: Expected {expected_chain_id}")
            return False
        else:
            print("   ✅ Correct SKALE Europa chain ID")
        
        # Check gas price (should be 0 on SKALE)
        gas_price = w3.eth.gas_price
        print(f"   Gas price: {gas_price}")
        if gas_price != 0:
            print(f"   ⚠️  WARNING: Gas price is not 0 ({gas_price})")
        else:
            print("   ✅ Gas price is 0 (correct for SKALE)")
        
        # Check latest block
        latest_block = w3.eth.get_block('latest')
        print(f"   Latest block: {latest_block['number']:,}")
        print(f"   Block gas limit: {latest_block['gasLimit']:,}")
        
        # Check if block gas limit is reasonable
        if latest_block['gasLimit'] < 8000000:  # 8M gas
            print(f"   ⚠️  WARNING: Low block gas limit")
            return False
        else:
            print("   ✅ Block gas limit is sufficient")
        
        return True
        
    except Exception as e:
        print(f"   ❌ NETWORK ERROR: {e}")
        return False

def main():
    print("🌐 Network/RPC Issues Diagnostic")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Network Congestion", test_network_congestion()))
    results.append(("RPC Endpoint", test_rpc_endpoint()))
    results.append(("Nonce Conflicts", test_nonce_conflicts()))
    results.append(("Network Requirements", test_network_requirements()))
    
    # Summary
    print("\n📊 Network Diagnostic Results")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n✅ All network tests passed - Network issues unlikely")
    else:
        print("\n❌ Some network tests failed - This could be the deployment issue")
    
    return all_passed

if __name__ == "__main__":
    main()
