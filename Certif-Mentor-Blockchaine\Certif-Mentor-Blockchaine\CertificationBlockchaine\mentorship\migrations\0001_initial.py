# Generated by Django 5.2.1 on 2025-05-29 13:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mentor_id', models.IntegerField()),
                ('mentor_name', models.CharField(max_length=100)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('meeting_type', models.CharField(choices=[('consultation', 'Consultation'), ('mentoring', 'Mentorat'), ('review', 'Révision'), ('project_help', 'Aide projet')], default='mentoring', max_length=20)),
                ('date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('duration_minutes', models.IntegerField(default=60)),
                ('status', models.CharField(choices=[('scheduled', 'Programmé'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('rescheduled', 'Reprogrammé')], default='scheduled', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('meeting_link', models.URLField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_meetings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['date', 'start_time'],
            },
        ),
    ]
