#!/usr/bin/env python3
"""
Test contract size limitations that could cause deployment failures
"""
import os
import sys
import django
import json
from solcx import compile_source, install_solc, set_solc_version

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def check_contract_size():
    """Check if the contract size exceeds blockchain limits"""
    print("🔍 Contract Size Analysis")
    print("=" * 50)
    
    try:
        # Read the contract source code
        contract_path = os.path.join(os.path.dirname(__file__), '..', '..', 'CertificateRegistry.sol')
        
        if not os.path.exists(contract_path):
            print(f"❌ Contract file not found at: {contract_path}")
            return False
        
        with open(contract_path, 'r') as f:
            contract_source = f.read()
        
        print(f"✅ Contract source loaded")
        print(f"   Source code lines: {len(contract_source.splitlines())}")
        print(f"   Source code size: {len(contract_source):,} characters")
        
        # Try to compile the contract
        try:
            # Install and set Solidity compiler
            print("📦 Installing Solidity compiler...")
            install_solc('0.8.19')
            set_solc_version('0.8.19')
            
            print("🔨 Compiling contract...")
            compiled_sol = compile_source(contract_source)
            
            # Get the contract interface
            contract_interface = compiled_sol['<stdin>:CertificateRegistry']
            
            # Get bytecode
            bytecode = contract_interface['bin']
            bytecode_size = len(bytecode) // 2  # Convert hex to bytes
            
            print(f"✅ Contract compiled successfully")
            print(f"   Bytecode size: {bytecode_size:,} bytes")
            print(f"   Bytecode hex length: {len(bytecode):,} characters")
            
            # Check against common size limits
            limits = {
                "Ethereum Mainnet": 24576,  # 24KB limit
                "Most EVM chains": 24576,
                "Some L2s": 32768,  # 32KB
                "SKALE (estimated)": 50000,  # 50KB (generous estimate)
            }
            
            print(f"\n📏 Size Limit Analysis:")
            size_issues = False
            for network, limit in limits.items():
                if bytecode_size > limit:
                    print(f"   ❌ {network}: {bytecode_size:,} > {limit:,} bytes (EXCEEDS LIMIT)")
                    size_issues = True
                else:
                    print(f"   ✅ {network}: {bytecode_size:,} < {limit:,} bytes (OK)")
            
            # Check ABI size
            abi = contract_interface['abi']
            abi_json = json.dumps(abi)
            abi_size = len(abi_json)
            
            print(f"\n📋 ABI Analysis:")
            print(f"   ABI functions: {len([item for item in abi if item['type'] == 'function'])}")
            print(f"   ABI events: {len([item for item in abi if item['type'] == 'event'])}")
            print(f"   ABI size: {abi_size:,} characters")
            
            if size_issues:
                print(f"\n❌ CONTRACT SIZE ISSUE DETECTED")
                print(f"   The contract is too large for some networks")
                print(f"   This could be causing deployment failures")
                return False
            else:
                print(f"\n✅ Contract size is within acceptable limits")
                return True
                
        except Exception as compile_error:
            print(f"❌ Compilation failed: {compile_error}")
            print(f"   This could indicate code issues preventing deployment")
            return False
            
    except Exception as e:
        print(f"❌ Error during size check: {e}")
        return False

def check_gas_estimation():
    """Check estimated gas for deployment"""
    print("\n🔍 Gas Estimation Analysis")
    print("=" * 50)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        
        # Try to estimate deployment gas
        print("⛽ Estimating deployment gas...")
        
        # Load contract source and ABI
        contract_path = os.path.join(os.path.dirname(__file__), '..', '..', 'CertificateRegistry.sol')
        abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
        
        if os.path.exists(abi_path):
            with open(abi_path, 'r') as f:
                contract_abi = json.load(f)
            
            # Try to create contract instance for gas estimation
            try:
                # This is a rough estimation approach
                print(f"   Contract ABI loaded with {len(contract_abi)} items")
                
                # Estimate based on contract complexity
                function_count = len([item for item in contract_abi if item['type'] == 'function'])
                event_count = len([item for item in contract_abi if item['type'] == 'event'])
                
                # Rough gas estimation based on complexity
                base_gas = 200000  # Base deployment cost
                function_gas = function_count * 50000  # ~50k per function
                storage_gas = 100000  # Storage setup
                
                estimated_gas = base_gas + function_gas + storage_gas
                
                print(f"   Functions: {function_count}")
                print(f"   Events: {event_count}")
                print(f"   Estimated deployment gas: {estimated_gas:,}")
                
                # Check against limits
                if estimated_gas > 8000000:  # 8M gas block limit
                    print(f"   ❌ Estimated gas exceeds typical block limit")
                    return False
                else:
                    print(f"   ✅ Estimated gas within reasonable limits")
                    return True
                    
            except Exception as e:
                print(f"   ⚠️  Could not estimate gas: {e}")
                return True  # Don't fail on estimation error
                
        else:
            print(f"   ⚠️  ABI file not found, skipping gas estimation")
            return True
            
    except Exception as e:
        print(f"❌ Gas estimation error: {e}")
        return True  # Don't fail on estimation error

def main():
    print("📏 Contract Size Limitations Check")
    print("=" * 60)
    
    results = []
    
    # Run size checks
    results.append(("Contract Size", check_contract_size()))
    results.append(("Gas Estimation", check_gas_estimation()))
    
    # Summary
    print("\n📊 Contract Size Analysis Results")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n✅ Contract size is not the issue")
    else:
        print("\n❌ CONTRACT SIZE ISSUES DETECTED - This is likely the deployment problem")
    
    return all_passed

if __name__ == "__main__":
    main()
