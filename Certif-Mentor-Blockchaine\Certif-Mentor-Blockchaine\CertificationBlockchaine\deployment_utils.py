"""
Standardized deployment utilities for contract deployment and address management
"""
import os
import re
import json
import logging
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)

class DeploymentManager:
    """Manages contract deployment and address updates across the codebase"""
    
    def __init__(self, project_root: str = None):
        """Initialize deployment manager
        
        Args:
            project_root: Root directory of the project
        """
        if project_root is None:
            project_root = os.path.dirname(__file__)
        self.project_root = project_root
        
        # Define all files that need contract address updates
        self.address_update_targets = [
            {
                'file': 'blockchain/blockchain_service.py',
                'patterns': [
                    {
                        'old_pattern': r'self\.contract_address = None.*',
                        'new_template': 'self.contract_address = "{address}"  # Deployed contract address'
                    },
                    {
                        'old_pattern': r'self\.contract_address = "[^"]*".*',
                        'new_template': 'self.contract_address = "{address}"  # Deployed contract address'
                    }
                ]
            }
        ]
    
    def update_contract_address(self, contract_address: str) -> Dict[str, bool]:
        """Update contract address across all relevant files
        
        Args:
            contract_address: The new contract address
            
        Returns:
            Dict mapping file paths to success status
        """
        results = {}
        
        for target in self.address_update_targets:
            file_path = os.path.join(self.project_root, target['file'])
            results[target['file']] = self._update_file_address(
                file_path, contract_address, target['patterns']
            )
        
        return results
    
    def _update_file_address(self, file_path: str, address: str, patterns: List[Dict]) -> bool:
        """Update contract address in a specific file
        
        Args:
            file_path: Path to the file to update
            address: New contract address
            patterns: List of pattern dictionaries with old_pattern and new_template
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                return False
            
            # Read current content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            updated = False
            
            # Try each pattern until one works
            for pattern_info in patterns:
                old_pattern = pattern_info['old_pattern']
                new_template = pattern_info['new_template']
                
                # Check if pattern matches
                if re.search(old_pattern, content):
                    # Replace using regex
                    new_line = new_template.format(address=address)
                    content = re.sub(old_pattern, new_line, content)
                    updated = True
                    logger.info(f"Updated contract address in {file_path} using pattern: {old_pattern}")
                    break
            
            if updated:
                # Write updated content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"✅ Successfully updated contract address in {file_path}")
                return True
            else:
                logger.warning(f"No matching pattern found in {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update {file_path}: {e}")
            return False
    
    def save_deployment_info(self, abi: List[Dict], contract_address: str, 
                           transaction_hash: str = None, gas_used: int = None) -> bool:
        """Save deployment information including ABI and contract address
        
        Args:
            abi: Contract ABI
            contract_address: Deployed contract address
            transaction_hash: Deployment transaction hash
            gas_used: Gas used for deployment
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Save ABI
            abi_path = os.path.join(self.project_root, 'blockchain', 'contract_abi.json')
            with open(abi_path, 'w') as f:
                json.dump(abi, f, indent=2)
            logger.info(f"✅ ABI saved to: {abi_path}")
            
            # Update contract addresses
            update_results = self.update_contract_address(contract_address)
            
            # Save deployment metadata
            deployment_info = {
                'contract_address': contract_address,
                'deployment_timestamp': int(__import__('time').time()),
                'transaction_hash': transaction_hash,
                'gas_used': gas_used,
                'abi_path': abi_path
            }
            
            metadata_path = os.path.join(self.project_root, 'deployment_info.json')
            with open(metadata_path, 'w') as f:
                json.dump(deployment_info, f, indent=2)
            logger.info(f"✅ Deployment metadata saved to: {metadata_path}")
            
            # Check if all updates were successful
            all_successful = all(update_results.values())
            if all_successful:
                logger.info("✅ All contract address updates completed successfully")
            else:
                failed_files = [f for f, success in update_results.items() if not success]
                logger.warning(f"⚠️ Some files failed to update: {failed_files}")
            
            return all_successful
            
        except Exception as e:
            logger.error(f"Failed to save deployment info: {e}")
            return False
    
    def get_current_contract_address(self) -> Optional[str]:
        """Get the currently configured contract address
        
        Returns:
            Current contract address or None if not found
        """
        try:
            service_path = os.path.join(self.project_root, 'blockchain', 'blockchain_service.py')
            with open(service_path, 'r') as f:
                content = f.read()
            
            # Look for contract address assignment
            match = re.search(r'self\.contract_address = "([^"]*)"', content)
            if match:
                address = match.group(1)
                return address if address and address != "None" else None
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get current contract address: {e}")
            return None
    
    def validate_deployment_environment(self) -> Dict[str, bool]:
        """Validate that the deployment environment is ready
        
        Returns:
            Dict with validation results
        """
        results = {
            'blockchain_service_exists': False,
            'abi_directory_exists': False,
            'contract_source_exists': False
        }
        
        try:
            # Check blockchain service
            service_path = os.path.join(self.project_root, 'blockchain', 'blockchain_service.py')
            results['blockchain_service_exists'] = os.path.exists(service_path)
            
            # Check ABI directory
            abi_dir = os.path.join(self.project_root, 'blockchain')
            results['abi_directory_exists'] = os.path.exists(abi_dir)
            
            # Check for contract source
            contract_paths = [
                os.path.join(self.project_root, '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol'),
                os.path.join(self.project_root, 'CertificateRegistry.sol')
            ]
            
            for path in contract_paths:
                if os.path.exists(path):
                    results['contract_source_exists'] = True
                    break
            
        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
        
        return results

# Global deployment manager instance
deployment_manager = DeploymentManager()
