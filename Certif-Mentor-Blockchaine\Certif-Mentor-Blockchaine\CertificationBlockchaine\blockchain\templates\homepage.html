{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Accueil | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Swiper CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
  <!-- Swiper JS -->
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <style>
    /* Notification Panel Styles */
    .notification-panel {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 400px;
      background: white;
      box-shadow: 2px 0 10px rgba(0,0,0,0.1);
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
      z-index: 1000;
      overflow-y: auto;
    }

    .notification-panel.open {
      transform: translateX(0);
    }

    .notification-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0,0,0,0.5);
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }

    .notification-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .notification-item {
      border-left: 4px solid transparent;
      transition: all 0.2s ease;
    }

    .notification-item.unread {
      border-left-color: #3b82f6;
      background-color: #eff6ff;
    }

    .notification-item:hover {
      background-color: #f3f4f6;
    }

    .notification-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #ef4444;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    /* User Menu Dropdown Styles */
    .user-menu {
      position: absolute;
      top: 100%;
      right: 0;
      width: 200px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease-in-out;
    }

    .user-menu.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .user-menu-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: background-color 0.2s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .user-menu-item:last-child {
      border-bottom: none;
    }

    .user-menu-item:hover {
      background-color: #f9fafb;
    }

    .user-menu-item:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    .user-menu-item:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    /* Navigation Button Styles */
    .nav-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1px;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: pointer;
      text-decoration: none;
      color: #6b7280;
    }

    .nav-button:hover {
      background-color: #f3f4f6;
      color: #3b82f6;
    }

    .nav-button.active {
      background-color: #eff6ff;
      color: #3b82f6;
    }

    .nav-button-icon {
      width: 24px;
      height: 24px;
    }

    .nav-button-text {
      font-size: 12px;
      font-weight: 500;
    }

    /* Default avatar styling */
    .mentor-avatar {
      transition: opacity 0.3s ease;
      object-fit: cover;
    }

    .mentor-avatar:hover {
      opacity: 0.9;
    }

    /* Logo hover effect */
    .logo-hover {
      transition: all 0.3s ease;
    }

    .logo-hover:hover {
      transform: scale(1.05);
    }

    /* Search dropdown styles */
    .search-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #e5e7eb;
      border-top: none;
      border-radius: 0 0 8px 8px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      max-height: 400px;
      overflow-y: auto;
      z-index: 1000;
      display: none;
    }

    .search-dropdown.show {
      display: block;
    }

    .search-result-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: background-color 0.2s ease;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .search-result-item:last-child {
      border-bottom: none;
    }

    .search-result-item:hover {
      background-color: #f9fafb;
    }

    .search-result-item.selected {
      background-color: #eff6ff;
    }

    .search-result-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .search-result-info {
      flex: 1;
    }

    .search-result-name {
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .search-result-description {
      font-size: 14px;
      color: #6b7280;
    }

    .search-no-results {
      padding: 16px;
      text-align: center;
      color: #6b7280;
      font-style: italic;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">

  <!-- Notification Overlay -->
  <div id="notificationOverlay" class="notification-overlay"></div>

  <!-- Notification Panel -->
  <div id="notificationPanel" class="notification-panel">
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <button id="closeNotificationPanel" class="p-1 hover:bg-gray-100 rounded">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m15 18-6-6 6-6"/>
            </svg>
          </button>
          <h2 class="text-xl font-bold text-gray-800">Notifications</h2>
        </div>
        <button id="closeNotificationPanelX" class="p-1 hover:bg-gray-100 rounded">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <div class="p-4">
      <div class="space-y-3">
        <!-- Notification Items -->
        <div class="notification-item unread p-4 rounded-lg cursor-pointer" data-id="1">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Nouvelle certification disponible</h3>
            <span class="text-xs text-gray-500">Aujourd'hui</span>
          </div>
          <p class="text-sm text-gray-600">Une nouvelle certification en Smart Contracts est maintenant disponible.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Certification</span>
          </div>
        </div>

        <div class="notification-item unread p-4 rounded-lg cursor-pointer" data-id="2">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Demande de mentorat acceptée</h3>
            <span class="text-xs text-gray-500">Hier</span>
          </div>
          <p class="text-sm text-gray-600">Sophie Dubois a accepté votre demande de mentorat en Blockchain.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Mentorat</span>
          </div>
        </div>

        <div class="notification-item unread p-4 rounded-lg cursor-pointer" data-id="3">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Rappel de session</h3>
            <span class="text-xs text-gray-500">Hier</span>
          </div>
          <p class="text-sm text-gray-600">Votre session de mentorat avec Jean Moreau commence dans 1 heure.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Rappel</span>
          </div>
        </div>

        <div class="notification-item p-4 rounded-lg cursor-pointer" data-id="4">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Compétence validée</h3>
            <span class="text-xs text-gray-500">23 Mai</span>
          </div>
          <p class="text-sm text-gray-600">Votre compétence en 'Déploiement Ethereum' a été validée par votre mentor.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Validation</span>
          </div>
        </div>

        <div class="notification-item p-4 rounded-lg cursor-pointer" data-id="5">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Nouvelle ressource partagée</h3>
            <span class="text-xs text-gray-500">20 Mai</span>
          </div>
          <p class="text-sm text-gray-600">Karim Benali a partagé une nouvelle ressource sur les NFTs avec vous.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Ressource</span>
          </div>
        </div>

        <div class="notification-item p-4 rounded-lg cursor-pointer" data-id="6">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-800">Message de mentor</h3>
            <span class="text-xs text-gray-500">18 Mai</span>
          </div>
          <p class="text-sm text-gray-600">Marie Lefebvre vous a envoyé un message concernant votre progression en DeFi.</p>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Message</span>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-200 p-4">
      <button id="markAllAsRead" class="w-full py-2 text-center text-blue-600 hover:text-blue-800 transition-colors font-medium">
        Marquer tout comme lu
      </button>
    </div>
  </div>

  <!-- Barre de navigation -->
  <header class="bg-white shadow-md py-6 px-8 flex justify-between items-center">
    <!-- Left side - Logo and Navigation -->
    <div class="flex items-center gap-8">
      <div id="logoButton2" class="text-3xl font-bold text-blue-600 cursor-pointer hover:text-blue-700 logo-hover">CertifMentor</div>

      {% if user.is_authenticated %}
      <!-- Navigation Buttons -->
      <div class="flex items-center gap-2">
        <!-- Notification Button -->
        <button id="notificationButton" class="nav-button relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>
          <span class="nav-button-text">Notifications</span>
          <span id="notificationBadge" class="notification-badge">3</span>
        </button>



        <!-- Calendar Button -->
        <button id="calendrierButton" class="nav-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
          <span class="nav-button-text">Calendrier</span>
        </button>

        <!-- Call Button -->
        <button id="appelButton" class="nav-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
          </svg>
          <span class="nav-button-text">Appel</span>
        </button>
      </div>
      {% endif %}
    </div>

    <!-- Right side - User Menu -->
    <div class="flex items-center gap-6">
      {% if user.is_authenticated %}
        <div class="relative">
          <button id="userMenuButton" class="text-gray-700 font-medium text-lg hover:text-blue-600 transition-colors cursor-pointer">
            👤 {{ user.first_name }} {{ user.last_name }}
          </button>

          <!-- User Menu Dropdown -->
          <div id="userMenu" class="user-menu">
            <div class="user-menu-item" id="profileButton">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              <span>Mon Profil</span>
            </div>
            <div class="user-menu-item" id="logoutButton">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                <polyline points="16,17 21,12 16,7"></polyline>
                <line x1="21" y1="12" x2="9" y2="12"></line>
              </svg>
              <span>Se déconnecter</span>
            </div>
            <div class="user-menu-item" id="securityButton">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <circle cx="12" cy="16" r="1"></circle>
                <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
              </svg>
              <span>Sécurité</span>
            </div>
            <div class="user-menu-item" id="becomeMentorButton">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="m22 2-5 10-5-4-5 10"></path>
              </svg>
              <span>Devenir mentor</span>
            </div>
          </div>
        </div>
      {% else %}
        <a href="{% url 'connexion' %}" class="text-blue-600 hover:underline mr-6 text-lg">Se connecter</a>
        <a href="{% url 'inscription' %}" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
      {% endif %}
    </div>
  </header>

  <!-- Section principale -->
  <main class="flex-grow w-full py-16">
    <h1 class="text-4xl font-bold text-center text-gray-800 mb-8 px-8">Trouvez votre mentor ou explorez nos cours</h1>

    <!-- Dual Search Bar Section -->
    <div class="px-8 mb-12">
      <div class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

          <!-- Left: Mentor Search -->
          <div class="relative">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 text-center">🧑‍🏫 Trouvez votre mentor</h3>
            <div class="flex items-center bg-white rounded-lg shadow-md border border-gray-200">
              <input
                type="text"
                id="searchInput"
                placeholder="Rechercher un mentor..."
                class="flex-1 px-4 py-3 text-gray-700 bg-transparent rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autocomplete="off"
              >
              <button
                id="searchButton"
                class="px-4 py-3 text-gray-500 hover:text-blue-600 transition-colors"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </button>
              <button
                id="filterButton"
                class="px-6 py-3 bg-green-600 text-white rounded-r-lg hover:bg-green-700 transition-colors font-medium"
                type="button"
              >
                Filter
              </button>
            </div>

            <!-- Mentor Search Dropdown -->
            <div id="searchDropdown" class="search-dropdown">
              <div id="searchResults"></div>
            </div>
          </div>

          <!-- Right: Course Search -->
          <div class="relative">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 text-center">📚 Explorez nos cours</h3>
            <div class="flex items-center bg-white rounded-lg shadow-md border border-gray-200">
              <input
                type="text"
                id="courseSearchInput"
                placeholder="Rechercher un cours..."
                class="flex-1 px-4 py-3 text-gray-700 bg-transparent rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autocomplete="off"
              >
              <button
                id="courseSearchButton"
                class="px-4 py-3 text-gray-500 hover:text-blue-600 transition-colors"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </button>
              <button
                id="courseFilterButton"
                class="px-6 py-3 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors font-medium"
                type="button"
              >
                Voir tous
              </button>
            </div>

            <!-- Course Search Dropdown -->
            <div id="courseSearchDropdown" class="search-dropdown">
              <div id="courseSearchResults"></div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Specializations Section -->
    <h1 class="text-4xl font-bold text-gray-800 mb-8 px-8">Choisissez votre spécialisation à cértifier</h1>

    <!-- Specializations Swiper Container -->
    <div class="swiper specializationSwiper w-full mb-16">
      <div class="swiper-wrapper">
        <!-- Smart Contracts Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="smart-contracts">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="9" cy="9" r="2"></circle>
                <path d="m21 15-3.086-3.086a2 2 0 0 0-1.414-.586H13"></path>
                <path d="M9 17v-2a2 2 0 0 1 2-2h2"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">Smart Contracts</h2>
            <p class="text-blue-100 mb-6">Maîtrisez le développement de contrats intelligents sur Ethereum et autres blockchains</p>
            <div class="flex items-center justify-center space-x-2 text-blue-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>

        <!-- DeFi Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-green-500 to-green-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="defi">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">Finance Décentralisée (DeFi)</h2>
            <p class="text-green-100 mb-6">Apprenez les protocoles DeFi, yield farming, et la création de produits financiers décentralisés</p>
            <div class="flex items-center justify-center space-x-2 text-green-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>

        <!-- NFT Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-purple-500 to-purple-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="nft">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="9" cy="9" r="2"></circle>
                <path d="m21 15-3.086-3.086a2 2 0 0 0-1.414-.586H13"></path>
                <path d="M9 17v-2a2 2 0 0 1 2-2h2"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">NFT & Métavers</h2>
            <p class="text-purple-100 mb-6">Créez, déployez et commercialisez des NFTs, explorez les applications métavers</p>
            <div class="flex items-center justify-center space-x-2 text-purple-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>

        <!-- Blockchain Development Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-orange-500 to-orange-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="blockchain-dev">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="16 18 22 12 16 6"></polyline>
                <polyline points="8 6 2 12 8 18"></polyline>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">Développement Blockchain</h2>
            <p class="text-orange-100 mb-6">Développez vos propres blockchains, consensus algorithms et architectures distribuées</p>
            <div class="flex items-center justify-center space-x-2 text-orange-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>

        <!-- Cryptography Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-red-500 to-red-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="cryptography">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <circle cx="12" cy="16" r="1"></circle>
                <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">Cryptographie & Sécurité</h2>
            <p class="text-red-100 mb-6">Maîtrisez les algorithmes cryptographiques, signatures numériques et sécurité blockchain</p>
            <div class="flex items-center justify-center space-x-2 text-red-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>

        <!-- Web3 Development Specialization -->
        <div class="swiper-slide px-4">
          <div class="bg-gradient-to-br from-indigo-500 to-indigo-700 rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-all transform hover:scale-105" data-specialization="web3">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="2" y1="12" x2="22" y2="12"></line>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4">Développement Web3</h2>
            <p class="text-indigo-100 mb-6">Créez des applications décentralisées (dApps) avec React, Web3.js et les dernières technologies</p>
            <div class="flex items-center justify-center space-x-2 text-indigo-100 text-sm">
              <span>🎓 Certification disponible</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Pagination -->
      <div class="swiper-pagination mt-8"></div>

      <!-- Add Navigation buttons -->
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
    </div>

    <h1 class="text-4xl font-bold text-gray-800 mb-8 px-8">Besoin d'un mentor ? </h1>

    <!-- Swiper Container - Full width, edge to edge -->
    <div class="swiper mentorSwiper w-full">
      <div class="swiper-wrapper">
        {% for mentor in mentors %}
        <!-- Carte mentor {{ mentor.id }} -->
        <div class="swiper-slide px-4">
          <div class="bg-white rounded-2xl shadow-lg p-8 text-center mx-2 cursor-pointer hover:shadow-xl transition-shadow" data-mentor-id="{{ mentor.id }}">
            <img src="{{ mentor.image }}" alt="{{ mentor.name }}" class="w-40 h-40 rounded-full mx-auto mb-6 mentor-avatar"
                 onerror="this.src='https://ui-avatars.com/api/?name={{ mentor.name|urlencode }}&size=200&background=e5e7eb&color=6b7280&bold=true'">
            <h2 class="text-xl font-semibold text-gray-800 mb-2">{{ mentor.name }}</h2>
            <p class="text-base text-gray-500">{{ mentor.description }}</p>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Add Pagination -->
      <div class="swiper-pagination mt-8"></div>

      <!-- Add Navigation buttons -->
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
    </div>

    <!-- Blockchain Certificate Registration Section -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 py-16 px-8 mt-16">
      <div class="max-w-4xl mx-auto text-center">
        <div class="mb-8">
          <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <circle cx="12" cy="16" r="1"></circle>
              <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
            </svg>
          </div>
          <h2 class="text-4xl font-bold text-white mb-4">Vous avez déjà un certificat ?</h2>
          <p class="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Enregistrez votre certificat existant sur la blockchain pour garantir son authenticité et sa vérifiabilité à vie.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 text-white">
          <div class="bg-white bg-opacity-10 rounded-lg p-6">
            <div class="text-3xl mb-3">🔒</div>
            <h3 class="text-lg font-semibold mb-2">Sécurisé</h3>
            <p class="text-purple-100 text-sm">Votre certificat est protégé par la technologie blockchain</p>
          </div>
          <div class="bg-white bg-opacity-10 rounded-lg p-6">
            <div class="text-3xl mb-3">✅</div>
            <h3 class="text-lg font-semibold mb-2">Vérifiable</h3>
            <p class="text-purple-100 text-sm">Authentification instantanée par n'importe qui, n'importe où</p>
          </div>
          <div class="bg-white bg-opacity-10 rounded-lg p-6">
            <div class="text-3xl mb-3">♾️</div>
            <h3 class="text-lg font-semibold mb-2">Permanent</h3>
            <p class="text-purple-100 text-sm">Stockage immuable et accessible à vie</p>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            id="registerCertificateButton"
            class="bg-white text-purple-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg"
          >
            🔗 Enregistrer mon certificat
          </button>
          <button
            id="verifyCertificateButton"
            class="bg-purple-500 bg-opacity-20 border-2 border-white text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-30 transition-all transform hover:scale-105 shadow-lg"
          >
            ✅ Vérifier un certificat
          </button>
        </div>
      </div>
    </div>
  </main>

  <!-- Pied de page -->
  <footer class="bg-white text-center py-6 text-base text-gray-500">
    &copy; 2025 CertifMentor. Tous droits réservés.
  </footer>

  <!-- Initialize Swiper -->
  <script>
    // Initialize Specialization Swiper
    var specializationSwiper = new Swiper(".specializationSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      loop: true,
      pagination: {
        el: ".specializationSwiper .swiper-pagination",
        clickable: true,
      },
      navigation: {
        nextEl: ".specializationSwiper .swiper-button-next",
        prevEl: ".specializationSwiper .swiper-button-prev",
      },
      breakpoints: {
        640: {
          slidesPerView: 1,
          spaceBetween: 20,
        },
        768: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        1024: {
          slidesPerView: 3,
          spaceBetween: 20,
        },
        1280: {
          slidesPerView: 4,
          spaceBetween: 20,
        },
      },
      autoplay: {
        delay: 4000,
        disableOnInteraction: false,
      },
    });

    // Initialize Mentor Swiper
    var mentorSwiper = new Swiper(".mentorSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      loop: true,
      pagination: {
        el: ".mentorSwiper .swiper-pagination",
        clickable: true,
      },
      navigation: {
        nextEl: ".mentorSwiper .swiper-button-next",
        prevEl: ".mentorSwiper .swiper-button-prev",
      },
      breakpoints: {
        640: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        768: {
          slidesPerView: 3,
          spaceBetween: 20,
        },
        1024: {
          slidesPerView: 4,
          spaceBetween: 20,
        },
        1280: {
          slidesPerView: 5,
          spaceBetween: 20,
        },
      },
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
    });

    // Notification Panel JavaScript
    document.addEventListener('DOMContentLoaded', function() {
      const notificationButton = document.getElementById('notificationButton');
      const notificationPanel = document.getElementById('notificationPanel');
      const notificationOverlay = document.getElementById('notificationOverlay');
      const closeNotificationPanel = document.getElementById('closeNotificationPanel');
      const closeNotificationPanelX = document.getElementById('closeNotificationPanelX');
      const markAllAsRead = document.getElementById('markAllAsRead');
      const notificationBadge = document.getElementById('notificationBadge');

      let unreadCount = 3;

      function updateBadge() {
        if (unreadCount > 0) {
          notificationBadge.textContent = unreadCount;
          notificationBadge.style.display = 'flex';
        } else {
          notificationBadge.style.display = 'none';
        }
      }

      function openPanel() {
        notificationPanel.classList.add('open');
        notificationOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
      }

      function closePanel() {
        notificationPanel.classList.remove('open');
        notificationOverlay.classList.remove('show');
        document.body.style.overflow = 'auto';
      }

      function markAsRead(notificationElement) {
        if (notificationElement.classList.contains('unread')) {
          notificationElement.classList.remove('unread');
          unreadCount--;
          updateBadge();
        }
      }

      function markAllNotificationsAsRead() {
        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
        unreadNotifications.forEach(notification => {
          notification.classList.remove('unread');
        });
        unreadCount = 0;
        updateBadge();
      }

      // Event listeners
      if (notificationButton) {
        notificationButton.addEventListener('click', openPanel);
      }
      if (closeNotificationPanel) {
        closeNotificationPanel.addEventListener('click', closePanel);
      }
      if (closeNotificationPanelX) {
        closeNotificationPanelX.addEventListener('click', closePanel);
      }
      if (notificationOverlay) {
        notificationOverlay.addEventListener('click', closePanel);
      }
      if (markAllAsRead) {
        markAllAsRead.addEventListener('click', markAllNotificationsAsRead);
      }

      // Mark individual notifications as read when clicked
      document.querySelectorAll('.notification-item').forEach(notification => {
        notification.addEventListener('click', function() {
          markAsRead(this);
        });
      });

      // Close panel with Escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && notificationPanel.classList.contains('open')) {
          closePanel();
        }
      });

      // Initialize badge
      updateBadge();
    });

    // Navigation Buttons JavaScript
    document.addEventListener('DOMContentLoaded', function() {
      // Calendar Button
      const calendrierButton = document.getElementById('calendrierButton');
      if (calendrierButton) {
        calendrierButton.addEventListener('click', function() {
          window.location.href = '/mentorship/calendar/';
        });
      }

      // Call Button
      const appelButton = document.getElementById('appelButton');
      if (appelButton) {
        appelButton.addEventListener('click', function() {
          // TODO: Add call functionality
          console.log('Call button clicked');
          // You can add your call page URL here later
          // window.location.href = '/appel/';
        });
      }
    });

    // User Menu JavaScript
    document.addEventListener('DOMContentLoaded', function() {
      const userMenuButton = document.getElementById('userMenuButton');
      const userMenu = document.getElementById('userMenu');

      if (userMenuButton && userMenu) {
        function toggleUserMenu() {
          userMenu.classList.toggle('show');
        }

        function closeUserMenu() {
          userMenu.classList.remove('show');
        }

        // Toggle menu when clicking the user button
        userMenuButton.addEventListener('click', function(e) {
          e.stopPropagation();
          toggleUserMenu();
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
          if (!userMenu.contains(e.target) && !userMenuButton.contains(e.target)) {
            closeUserMenu();
          }
        });

        // Close menu with Escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            closeUserMenu();
          }
        });

        // Add event listeners for user menu items
        const profileButton = document.getElementById('profileButton');
        const logoutButton = document.getElementById('logoutButton');
        const securityButton = document.getElementById('securityButton');
        const becomeMentorButton = document.getElementById('becomeMentorButton');

        if (profileButton) {
          profileButton.addEventListener('click', function() {
            window.location.href = '/users/profile/';
          });
        }

        if (logoutButton) {
          logoutButton.addEventListener('click', function() {
            window.location.href = '/deconnexion/';
          });
        }

        if (securityButton) {
          securityButton.addEventListener('click', function() {
            // TODO: Add security page URL when available
            console.log('Security clicked');
          });
        }

        if (becomeMentorButton) {
          becomeMentorButton.addEventListener('click', function() {
            // TODO: Add become mentor page URL when available
            console.log('Become mentor clicked');
          });
        }
      }
    });

    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
      const searchInput = document.getElementById('searchInput');
      const searchButton = document.getElementById('searchButton');
      const filterButton = document.getElementById('filterButton');
      const searchDropdown = document.getElementById('searchDropdown');
      const searchResults = document.getElementById('searchResults');

      // Mentor data for search (same as in views.py)
      const mentors = JSON.parse('{{ mentors_json|escapejs }}');

      // Course data for search
      const courses = [
        {
          id: 'smart-contracts',
          name: 'Smart Contracts',
          description: 'Maîtrisez le développement de contrats intelligents sur Ethereum',
          price: 299,
          level: 'Intermédiaire'
        },
        {
          id: 'defi',
          name: 'DeFi (Finance Décentralisée)',
          description: 'Développez des protocoles DeFi et comprenez l\'écosystème',
          price: 349,
          level: 'Avancé'
        },
        {
          id: 'nft',
          name: 'NFT & Métavers',
          description: 'Créez et déployez des collections NFT innovantes',
          price: 279,
          level: 'Intermédiaire'
        },
        {
          id: 'blockchain-development',
          name: 'Développement Blockchain',
          description: 'Construisez votre propre blockchain from scratch',
          price: 399,
          level: 'Expert'
        },
        {
          id: 'cryptography',
          name: 'Cryptographie & Sécurité',
          description: 'Maîtrisez les algorithmes cryptographiques et la sécurité',
          price: 329,
          level: 'Avancé'
        },
        {
          id: 'web3-development',
          name: 'Développement Web3',
          description: 'Créez des applications décentralisées modernes',
          price: 299,
          level: 'Intermédiaire'
        }
      ];

      let selectedIndex = -1;
      let currentResults = [];
      let courseSelectedIndex = -1;
      let currentCourseResults = [];

      // Search functionality
      function performSearch(query = null) {
        const searchTerm = query || searchInput.value.trim().toLowerCase();

        if (searchTerm.length === 0) {
          hideDropdown();
          return;
        }

        // Filter mentors based on search term (names only)
        currentResults = mentors.filter(mentor =>
          mentor.name.toLowerCase().includes(searchTerm)
        );

        displaySearchResults(currentResults);
        showDropdown();
      }

      function displaySearchResults(results) {
        if (results.length === 0) {
          searchResults.innerHTML = '<div class="search-no-results">Aucun mentor trouvé</div>';
          return;
        }

        searchResults.innerHTML = results.map((mentor, index) => `
          <div class="search-result-item" data-mentor-id="${mentor.id}" data-index="${index}">
            <img src="${mentor.image}" alt="${mentor.name}" class="search-result-avatar"
                 onerror="this.src='https://ui-avatars.com/api/?name=${encodeURIComponent(mentor.name)}&size=80&background=e5e7eb&color=6b7280&bold=true'">
            <div class="search-result-info">
              <div class="search-result-name">${mentor.name}</div>
              <div class="search-result-description">${mentor.description}</div>
            </div>
          </div>
        `).join('');

        // Add click event listeners to search results
        document.querySelectorAll('.search-result-item').forEach(item => {
          item.addEventListener('click', function() {
            const mentorId = this.getAttribute('data-mentor-id');
            window.location.href = '/mentor/' + mentorId + '/';
          });
        });
      }

      function showDropdown() {
        searchDropdown.classList.add('show');
      }

      function hideDropdown() {
        searchDropdown.classList.remove('show');
        selectedIndex = -1;
        updateSelection();
      }

      function updateSelection() {
        document.querySelectorAll('.search-result-item').forEach((item, index) => {
          if (index === selectedIndex) {
            item.classList.add('selected');
          } else {
            item.classList.remove('selected');
          }
        });
      }

      function navigateResults(direction) {
        if (currentResults.length === 0) return;

        if (direction === 'down') {
          selectedIndex = Math.min(selectedIndex + 1, currentResults.length - 1);
        } else if (direction === 'up') {
          selectedIndex = Math.max(selectedIndex - 1, -1);
        }

        updateSelection();
      }

      function selectCurrentResult() {
        if (selectedIndex >= 0 && selectedIndex < currentResults.length) {
          const selectedMentor = currentResults[selectedIndex];
          window.location.href = '/mentor/' + selectedMentor.id + '/';
        }
      }

      // Filter functionality - redirect to filter page
      function showFilters() {
        window.location.href = '/filter/';
      }

      // Event listeners
      if (searchInput) {
        // Real-time search as user types
        searchInput.addEventListener('input', function() {
          performSearch();
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateResults('down');
          } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateResults('up');
          } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedIndex >= 0) {
              selectCurrentResult();
            } else {
              // If no selection, go to filter page with search term
              const searchTerm = searchInput.value.trim();
              if (searchTerm) {
                window.location.href = '/filter/?search=' + encodeURIComponent(searchTerm);
              }
            }
          } else if (e.key === 'Escape') {
            hideDropdown();
            searchInput.blur();
          }
        });

        // Hide dropdown when input loses focus (with delay for clicks)
        searchInput.addEventListener('blur', function() {
          setTimeout(() => {
            hideDropdown();
          }, 200);
        });

        // Show dropdown when input gains focus (if there's content)
        searchInput.addEventListener('focus', function() {
          if (searchInput.value.trim().length > 0) {
            performSearch();
          }
        });
      }

      if (searchButton) {
        searchButton.addEventListener('click', function() {
          const searchTerm = searchInput.value.trim();
          if (searchTerm) {
            window.location.href = '/filter/?search=' + encodeURIComponent(searchTerm);
          }
        });
      }

      if (filterButton) {
        filterButton.addEventListener('click', showFilters);
      }

      // Course search functionality
      const courseSearchInput = document.getElementById('courseSearchInput');
      const courseSearchButton = document.getElementById('courseSearchButton');
      const courseFilterButton = document.getElementById('courseFilterButton');
      const courseSearchDropdown = document.getElementById('courseSearchDropdown');
      const courseSearchResults = document.getElementById('courseSearchResults');

      // Course search functionality
      function performCourseSearch(query = null) {
        const searchTerm = query || courseSearchInput.value.trim().toLowerCase();

        if (searchTerm.length === 0) {
          hideCourseDropdown();
          return;
        }

        // Filter courses based on search term
        currentCourseResults = courses.filter(course =>
          course.name.toLowerCase().includes(searchTerm) ||
          course.description.toLowerCase().includes(searchTerm)
        );

        displayCourseSearchResults(currentCourseResults);
        showCourseDropdown();
      }

      function displayCourseSearchResults(results) {
        if (results.length === 0) {
          courseSearchResults.innerHTML = '<div class="search-no-results">Aucun cours trouvé</div>';
          return;
        }

        courseSearchResults.innerHTML = results.map((course, index) => `
          <div class="search-result-item" data-course-id="${course.id}" data-index="${index}">
            <div class="search-result-avatar" style="background: #eff6ff; display: flex; align-items: center; justify-content: center; border: 2px solid #dbeafe;">
              <span class="text-blue-600 font-bold text-lg">📚</span>
            </div>
            <div class="search-result-info">
              <div class="search-result-name">${course.name}</div>
              <div class="search-result-description">${course.description}</div>
              <div style="font-size: 0.875rem; margin-top: 4px;">
                <span class="text-blue-600 font-semibold">${course.price}€</span>
                <span class="text-gray-500 ml-2">${course.level}</span>
              </div>
            </div>
          </div>
        `).join('');

        // Add click event listeners to course search results
        document.querySelectorAll('#courseSearchResults .search-result-item').forEach(item => {
          item.addEventListener('click', function() {
            const courseId = this.getAttribute('data-course-id');
            window.location.href = '/certification/' + courseId + '/';
          });
        });
      }

      function showCourseDropdown() {
        courseSearchDropdown.classList.add('show');
      }

      function hideCourseDropdown() {
        courseSearchDropdown.classList.remove('show');
        courseSelectedIndex = -1;
        updateCourseSelection();
      }

      function updateCourseSelection() {
        document.querySelectorAll('#courseSearchResults .search-result-item').forEach((item, index) => {
          if (index === courseSelectedIndex) {
            item.classList.add('selected');
          } else {
            item.classList.remove('selected');
          }
        });
      }

      // Course search event listeners
      if (courseSearchInput) {
        courseSearchInput.addEventListener('input', function() {
          performCourseSearch();
        });

        courseSearchInput.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateCourseResults('down');
          } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateCourseResults('up');
          } else if (e.key === 'Enter') {
            e.preventDefault();
            if (courseSelectedIndex >= 0) {
              selectCurrentCourseResult();
            }
          } else if (e.key === 'Escape') {
            hideCourseDropdown();
            courseSearchInput.blur();
          }
        });

        courseSearchInput.addEventListener('blur', function() {
          setTimeout(() => {
            hideCourseDropdown();
          }, 200);
        });

        courseSearchInput.addEventListener('focus', function() {
          if (courseSearchInput.value.trim().length > 0) {
            performCourseSearch();
          }
        });
      }

      if (courseSearchButton) {
        courseSearchButton.addEventListener('click', function() {
          const searchTerm = courseSearchInput.value.trim();
          if (searchTerm) {
            // Show all matching courses
            performCourseSearch();
          }
        });
      }

      if (courseFilterButton) {
        courseFilterButton.addEventListener('click', function() {
          // Show all courses
          displayCourseSearchResults(courses);
          showCourseDropdown();
        });
      }

      function navigateCourseResults(direction) {
        if (currentCourseResults.length === 0) return;

        if (direction === 'down') {
          courseSelectedIndex = courseSelectedIndex < currentCourseResults.length - 1 ? courseSelectedIndex + 1 : 0;
        } else {
          courseSelectedIndex = courseSelectedIndex > 0 ? courseSelectedIndex - 1 : currentCourseResults.length - 1;
        }
        updateCourseSelection();
      }

      function selectCurrentCourseResult() {
        if (courseSelectedIndex >= 0 && currentCourseResults[courseSelectedIndex]) {
          const course = currentCourseResults[courseSelectedIndex];
          window.location.href = '/certification/' + course.id + '/';
        }
      }

      // Close dropdown when clicking outside
      document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchDropdown.contains(e.target)) {
          hideDropdown();
        }
      });
    });

    // Blockchain Certificate Buttons
    document.addEventListener('DOMContentLoaded', function() {
      const registerCertificateButton = document.getElementById('registerCertificateButton');
      const verifyCertificateButton = document.getElementById('verifyCertificateButton');

      if (registerCertificateButton) {
        registerCertificateButton.addEventListener('click', function() {
          window.location.href = '/register-certificate/';
        });
      }

      if (verifyCertificateButton) {
        verifyCertificateButton.addEventListener('click', function() {
          window.location.href = '/verify-certificate/';
        });
      }
    });

    // Logo click functionality
    document.addEventListener('DOMContentLoaded', function() {
      const logoButton = document.getElementById('logoButton');
      const logoButton2 = document.getElementById('logoButton2');

      if (logoButton) {
        logoButton.addEventListener('click', function() {
          window.location.href = '/';
        });
      }

      if (logoButton2) {
        logoButton2.addEventListener('click', function() {
          window.location.href = '/';
        });
      }
    });

    // Mentor card click functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Add click event listeners to all mentor cards
      document.querySelectorAll('[data-mentor-id]').forEach(function(card) {
        card.addEventListener('click', function() {
          const mentorId = this.getAttribute('data-mentor-id');
          window.location.href = '/mentor/' + mentorId + '/';
        });
      });
    });

    // Specialization card click functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Add click event listeners to all specialization cards
      document.querySelectorAll('[data-specialization]').forEach(function(card) {
        card.addEventListener('click', function() {
          const specialization = this.getAttribute('data-specialization');

          // Redirect to certification page
          window.location.href = '/certification/' + specialization + '/';
        });
      });
    });
  </script>
</body>
</html>