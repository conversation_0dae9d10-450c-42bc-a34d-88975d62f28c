{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>{{ certification.name }} | CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Logo hover effect */
    .logo-hover {
      transition: all 0.3s ease;
    }

    .logo-hover:hover {
      transform: scale(1.05);
    }

    /* Progress bar animation */
    .progress-bar {
      transition: width 0.3s ease;
    }

    /* Card hover effects */
    .card-hover {
      transition: all 0.3s ease;
    }

    .card-hover:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">

  <!-- Barre de navigation -->
  <header class="bg-white shadow-md py-6 px-8 flex justify-between items-center">
    <!-- Left side - Logo -->
    <div class="flex items-center gap-8">
      <div id="logoButton" class="text-3xl font-bold text-blue-600 cursor-pointer hover:text-blue-700 logo-hover">CertifMentor</div>
    </div>

    <!-- Right side - User Menu -->
    <div class="flex items-center gap-6">
      {% if user.is_authenticated %}
        <div class="text-gray-700 font-medium text-lg">
          👤 {{ user.first_name }} {{ user.last_name }}
        </div>
      {% else %}
        <a href="{% url 'connexion' %}" class="text-blue-600 hover:underline mr-6 text-lg">Se connecter</a>
        <a href="{% url 'inscription' %}" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
      {% endif %}
    </div>
  </header>

  <!-- Section principale -->
  <main class="flex-grow w-full py-16">
    <div class="max-w-6xl mx-auto px-8">

      <!-- Header Section -->
      <div class="text-center mb-12">
        <h1 class="text-5xl font-bold text-gray-800 mb-4">Certification {{ certification.name }}</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ certification.description }}</p>
      </div>

      <!-- Key Information Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12 max-w-2xl mx-auto">
        <!-- Price Card -->
        <div class="bg-white rounded-lg shadow-md p-6 text-center card-hover">
          <div class="text-3xl font-bold text-blue-600 mb-2">{{ certification.price }}€</div>
          <div class="text-gray-600">Prix du test</div>
        </div>

        <!-- Level Card -->
        <div class="bg-white rounded-lg shadow-md p-6 text-center card-hover">
          <div class="text-lg font-bold text-purple-600 mb-2">{{ certification.level }}</div>
          <div class="text-gray-600">Niveau requis</div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

        <!-- Left Column - Main Information -->
        <div class="lg:col-span-2 space-y-8">

          <!-- Learning Objectives -->
          <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">🎯 Compétences évaluées</h2>
            <ul class="space-y-3">
              {% for objective in certification.learning_objectives %}
              <li class="flex items-start">
                <div class="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span class="text-gray-700">{{ objective }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>

          <!-- Curriculum -->
          <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">📚 Domaines d'évaluation</h2>
            <div class="space-y-6">
              {% for module in certification.curriculum %}
              <div class="border-l-4 border-blue-500 pl-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ module.module }}</h3>
                <div class="flex flex-wrap gap-2">
                  {% for topic in module.topics %}
                  <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">{{ topic }}</span>
                  {% endfor %}
                </div>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Prerequisites -->
          <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">✅ Prérequis</h2>
            <ul class="space-y-3">
              {% for prerequisite in certification.prerequisites %}
              <li class="flex items-start">
                <div class="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span class="text-gray-700">{{ prerequisite }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>

        <!-- Right Column - Enrollment Information -->
        <div class="space-y-8">

          <!-- Enrollment Card -->
          <div class="bg-white rounded-lg shadow-md p-8 sticky top-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">📝 Passer le test</h2>

            <!-- Price Display -->
            <div class="bg-blue-50 rounded-lg p-4 mb-6">
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600">{{ certification.price }}€</div>
                <div class="text-sm text-gray-600">Test d'évaluation</div>
              </div>
            </div>

            <!-- Test Info -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 class="font-semibold text-gray-800 mb-2">📋 À propos du test</h3>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• Test pratique évalué par des mentors experts</li>
                <li>• Correction personnalisée et détaillée</li>
                <li>• Certificat officiel en cas de réussite</li>
                <li>• Feedback constructif pour progresser</li>
              </ul>
            </div>

            <!-- Signup Button -->
            <button id="enrollButton"
                    data-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}"
                    data-specialization="{{ specialization }}"
                    data-price="{{ certification.price }}"
                    class="w-full bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 transition-colors font-bold text-lg mb-4">
              Passer le test
            </button>

            <p class="text-xs text-gray-500 text-center">
              Garantie satisfait ou remboursé 30 jours
            </p>
          </div>

          <!-- Certification Benefits -->
          <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">🏆 Avantages de la certification</h2>
            <ul class="space-y-3">
              {% for benefit in certification.certification_benefits %}
              <li class="flex items-start">
                <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span class="text-gray-700">{{ benefit }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Pied de page -->
  <footer class="bg-white text-center py-6 text-base text-gray-500">
    &copy; 2025 CertifMentor. Tous droits réservés.
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Logo click functionality
      const logoButton = document.getElementById('logoButton');

      if (logoButton) {
        logoButton.addEventListener('click', function() {
          window.location.href = '/';
        });
      }

      // Enrollment button functionality
      const enrollButton = document.getElementById('enrollButton');

      if (enrollButton) {
        enrollButton.addEventListener('click', function() {
          const isAuthenticated = this.dataset.authenticated === 'true';
          const specialization = this.dataset.specialization;
          const price = this.dataset.price;

          console.log('=== ENROLLMENT BUTTON CLICKED ===');
          console.log('Is authenticated:', isAuthenticated);
          console.log('Specialization:', specialization);
          console.log('Price:', price);

          if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            // Redirect to login page if user is not authenticated
            window.location.href = "/connexion/";
          } else {
            // For now, show a confirmation alert
            // Later this can be replaced with actual test enrollment functionality
            const confirmMessage = `Confirmer l'inscription au test de certification "${specialization}" pour ${price}€ ?\n\nCette fonctionnalité sera bientôt disponible avec:\n• Paiement sécurisé\n• Accès immédiat au test\n• Correction par des mentors experts\n• Certificat en cas de réussite`;

            if (confirm(confirmMessage)) {
              alert('Inscription au test confirmée ! Vous recevrez bientôt un email avec les instructions.\n\n(Fonctionnalité de paiement en cours de développement)');
            }
          }
        });
      }

      // Add smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // Add animation to cards on scroll
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, observerOptions);

      // Observe all cards
      document.querySelectorAll('.card-hover').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
      });
    });
  </script>
</body>
</html>
