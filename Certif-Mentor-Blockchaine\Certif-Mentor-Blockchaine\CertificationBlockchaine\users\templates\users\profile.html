<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mon Profil - CertifMentor</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
            CertifMentor
          </a>
        </div>

        <!-- Navigation -->
        <nav class="flex items-center space-x-6">
          <!-- Conversation Button -->
          <button id="conversationButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
            </svg>
            <span class="nav-button-text">Conversations</span>
          </button>

          <!-- Calendar Button -->
          <button id="calendrierButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            <span class="nav-button-text">Calendrier</span>
          </button>

          <!-- Mentorship Button -->
          <button id="mentorshipButton" class="nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-button-icon">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="m22 2-5 10-5-4-5 10"></path>
            </svg>
            <span class="nav-button-text">Mentorat</span>
          </button>
        </nav>

        <!-- User Menu -->
        {% if user.is_authenticated %}
          <div class="relative">
            <button id="userMenuButton" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
              <span class="mr-2 text-lg">{{ user.first_name|default:user.username }}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
              <div class="py-2">
                <div class="user-menu-item bg-blue-50 text-blue-600" id="profileButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  <span>Mon Profil</span>
                </div>
                <div class="user-menu-item" id="logoutButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16,17 21,12 16,7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                  <span>Déconnexion</span>
                </div>
                <div class="user-menu-item" id="securityButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <circle cx="12" cy="16" r="1"></circle>
                    <path d="m7 11 0-5a5 5 0 0 1 10 0v5"></path>
                  </svg>
                  <span>Sécurité</span>
                </div>
                <div class="user-menu-item" id="becomeMentorButton">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="m22 2-5 10-5-4-5 10"></path>
                  </svg>
                  <span>Devenir mentor</span>
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <div class="flex items-center space-x-4">
            <a href="/connexion/" class="text-blue-600 hover:underline text-lg">Se connecter</a>
            <a href="/inscription/" class="text-white bg-blue-600 px-6 py-3 rounded-lg hover:bg-blue-700 text-lg">S'inscrire</a>
          </div>
        {% endif %}
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-gray-900">Mon Profil</h1>
          <a href="{% url 'users:edit_profile' %}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Modifier le profil
          </a>
        </div>
      </div>

      <!-- Profile Content -->
      <div class="p-6">
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- Profile Image and Basic Info -->
          <div class="lg:w-1/3">
            <div class="text-center">
              <img src="{{ profile.avatar_url }}" alt="{{ profile.full_name }}"
                   class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900 mb-1">{{ profile.full_name }}</h2>
              <p class="text-gray-600 mb-2">{{ user.email }}</p>
              {% if profile.profession %}
                <p class="text-gray-500">{{ profile.profession }}</p>
              {% endif %}
            </div>

            <!-- Quick Stats -->
            <div class="mt-6 bg-gray-50 rounded-lg p-4">
              <h3 class="font-semibold text-gray-900 mb-3">Informations</h3>
              <div class="space-y-2 text-sm">

                {% if profile.phone %}
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                    {{ profile.phone }}
                  </div>
                {% endif %}
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="m22 2-5 10-5-4-5 10"></path>
                  </svg>
                  {{ profile.get_experience_level_display }}
                </div>
              </div>
            </div>
          </div>

          <!-- Profile Details -->
          <div class="lg:w-2/3">
            <!-- Bio Section -->
            {% if profile.bio %}
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">À propos</h3>
                <p class="text-gray-700 leading-relaxed">{{ profile.bio }}</p>
              </div>
            {% endif %}



            <!-- Interests -->
            {% if profile.interests_list %}
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Domaines d'intérêt</h3>
                <div class="flex flex-wrap gap-2">
                  {% for interest in profile.interests_list %}
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">{{ interest }}</span>
                  {% endfor %}
                </div>
              </div>
            {% endif %}

            <!-- Social Links -->
            {% if profile.linkedin_url or profile.github_url or profile.twitter_url or profile.website_url %}
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Liens</h3>
                <div class="flex flex-wrap gap-4">
                  {% if profile.linkedin_url %}
                    <a href="{{ profile.linkedin_url }}" target="_blank" class="flex items-center text-blue-600 hover:text-blue-700">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="mr-2">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                      LinkedIn
                    </a>
                  {% endif %}
                  {% if profile.github_url %}
                    <a href="{{ profile.github_url }}" target="_blank" class="flex items-center text-gray-700 hover:text-gray-900">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="mr-2">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                      GitHub
                    </a>
                  {% endif %}
                  {% if profile.twitter_url %}
                    <a href="{{ profile.twitter_url }}" target="_blank" class="flex items-center text-blue-400 hover:text-blue-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="mr-2">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                      Twitter
                    </a>
                  {% endif %}
                  {% if profile.website_url %}
                    <a href="{{ profile.website_url }}" target="_blank" class="flex items-center text-gray-600 hover:text-gray-800">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="2" y1="12" x2="22" y2="12"></line>
                        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                      </svg>
                      Site web
                    </a>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </main>

  <style>
    .nav-button {
      @apply flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors;
    }

    .nav-button-icon {
      @apply w-5 h-5;
    }

    .nav-button-text {
      @apply text-sm font-medium;
    }

    .user-menu-item {
      @apply flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer transition-colors;
    }
  </style>

  <script>
    // Navigation functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Conversation Button
      const conversationButton = document.getElementById('conversationButton');
      if (conversationButton) {
        conversationButton.addEventListener('click', function() {
          window.location.href = '/conversations/';
        });
      }

      // Calendar Button
      const calendrierButton = document.getElementById('calendrierButton');
      if (calendrierButton) {
        calendrierButton.addEventListener('click', function() {
          window.location.href = '/mentorship/calendar/';
        });
      }

      // Mentorship Button
      const mentorshipButton = document.getElementById('mentorshipButton');
      if (mentorshipButton) {
        mentorshipButton.addEventListener('click', function() {
          window.location.href = '/mentorship/';
        });
      }

      // User Menu
      const userMenuButton = document.getElementById('userMenuButton');
      const userMenu = document.getElementById('userMenu');

      if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
          e.stopPropagation();
          userMenu.classList.toggle('hidden');
        });

        document.addEventListener('click', function() {
          userMenu.classList.add('hidden');
        });

        userMenu.addEventListener('click', function(e) {
          e.stopPropagation();
        });

        // Profile button (already on profile page)
        const profileButton = document.getElementById('profileButton');
        if (profileButton) {
          profileButton.addEventListener('click', function() {
            // Already on profile page, just close menu
            userMenu.classList.add('hidden');
          });
        }

        // Logout functionality
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
          logoutButton.addEventListener('click', function() {
            window.location.href = '/deconnexion/';
          });
        }

        // Security functionality
        const securityButton = document.getElementById('securityButton');
        if (securityButton) {
          securityButton.addEventListener('click', function() {
            console.log('Security clicked');
          });
        }

        // Become mentor functionality
        const becomeMentorButton = document.getElementById('becomeMentorButton');
        if (becomeMentorButton) {
          becomeMentorButton.addEventListener('click', function() {
            console.log('Become mentor clicked');
          });
        }
      }
    });
  </script>
</body>
</html>
