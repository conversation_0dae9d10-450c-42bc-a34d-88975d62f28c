#!/usr/bin/env python3
"""
Test deployment with a very simple contract
"""
from web3 import Web3
from solcx import compile_source, install_solc
from eth_account import Account

def test_simple_deploy():
    print("🧪 Testing Simple Contract Deployment")
    print("=" * 40)
    
    # Simple test contract
    simple_contract = """
    // SPDX-License-Identifier: MIT
    pragma solidity ^0.8.19;
    
    contract SimpleTest {
        uint256 public value;
        
        constructor() {
            value = 42;
        }
        
        function setValue(uint256 _value) public {
            value = _value;
        }
        
        function getValue() public view returns (uint256) {
            return value;
        }
    }
    """
    
    # Configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    private_key = "0xdfd717932012eeed79652b9efe5ba7a4de232c3d26e37f99ecc6bb094b159bc9"
    
    # Setup
    account = Account.from_key(private_key)
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Connection failed")
        return False
    
    print("✅ Connected to Europa mainnet")
    
    # Install compiler
    try:
        install_solc('0.8.19')
    except:
        pass
    
    # Compile simple contract
    try:
        compiled_sol = compile_source(
            simple_contract,
            output_values=['abi', 'bin'],
            solc_version='0.8.19'
        )
        
        contract_id, contract_interface = compiled_sol.popitem()
        abi = contract_interface['abi']
        bytecode = contract_interface['bin']
        print("✅ Simple contract compiled")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Deploy
    try:
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)
        nonce = w3.eth.get_transaction_count(account.address)
        
        # Build transaction with lower gas
        constructor_txn = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': 500000,  # Much lower gas for simple contract
            'gasPrice': w3.eth.gas_price,
            'nonce': nonce,
            'from': account.address
        })
        
        # Sign and send
        signed_txn = w3.eth.account.sign_transaction(constructor_txn, private_key=private_key)
        
        print("📤 Deploying simple contract...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
        print(f"📤 TX: {tx_hash.hex()}")
        
        # Wait
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=60)
        
        if tx_receipt.status == 1:
            print("✅ Simple contract deployed successfully!")
            print(f"📍 Address: {tx_receipt.contractAddress}")
            print(f"⛽ Gas used: {tx_receipt.gasUsed}")
            
            # Test the contract
            simple_contract_instance = w3.eth.contract(
                address=tx_receipt.contractAddress,
                abi=abi
            )
            
            value = simple_contract_instance.functions.getValue().call()
            print(f"✅ Contract test: getValue() = {value}")
            
            return True
        else:
            print("❌ Simple contract deployment failed")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

if __name__ == "__main__":
    if test_simple_deploy():
        print("\n🎉 Simple deployment works!")
        print("The issue might be with the certificate contract complexity.")
    else:
        print("\n❌ Even simple deployment failed.")
        print("There might be a network or configuration issue.")
