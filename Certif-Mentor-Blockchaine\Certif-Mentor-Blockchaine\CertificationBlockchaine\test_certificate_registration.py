#!/usr/bin/env python3
"""
Test certificate registration through Django application
"""
import os
import sys
import django
import hashlib
from datetime import datetime

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

from blockchain.blockchain_service import blockchain_service

def test_certificate_registration():
    """Test registering a certificate through the Django service"""
    print("🧪 Testing Certificate Registration via Django")
    print("=" * 50)
    
    # Test certificate data
    test_data = {
        'name': 'Test Certificate - Real Blockchain',
        'issuer': 'SKALE Europa Test Institute',
        'recipient': '<PERSON>',
        'issue_date': datetime(2025, 1, 1),
        'certificate_number': 'SKALE-TEST-001',
        'description': 'This is a test certificate to verify real blockchain functionality on SKALE Europa',
        'file_content': b'This is test certificate content for blockchain verification'
    }
    
    print("📋 Test Certificate Details:")
    print(f"   Name: {test_data['name']}")
    print(f"   Issuer: {test_data['issuer']}")
    print(f"   Recipient: {test_data['recipient']}")
    print(f"   Certificate Number: {test_data['certificate_number']}")
    
    # Calculate file hash
    file_hash = hashlib.sha256(test_data['file_content']).hexdigest()
    print(f"   File Hash: {file_hash}")
    
    try:
        print("\n🚀 Registering certificate on SKALE Europa blockchain...")
        
        # Register certificate using the blockchain service
        result = blockchain_service.register_certificate(
            name=test_data['name'],
            issuer=test_data['issuer'],
            recipient=test_data['recipient'],
            issue_date=test_data['issue_date'],
            certificate_number=test_data['certificate_number'],
            description=test_data['description'],
            file_hash=file_hash
        )
        
        if result['success']:
            print("✅ Certificate registered successfully!")
            print(f"   📍 Certificate ID: {result['certificate_id']}")
            print(f"   🔗 Transaction Hash: {result['transaction_hash']}")
            print(f"   ⛽ Gas Used: {result['gas_used']}")
            print(f"   🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{result['transaction_hash']}")
            
            # Test verification
            print("\n🔍 Testing certificate verification...")
            verification_result = blockchain_service.verify_certificate_by_hash(file_hash)
            
            if verification_result['success']:
                print("✅ Certificate verification successful!")
                print(f"   Certificate exists: {verification_result['exists']}")
                print(f"   Name: {verification_result['name']}")
                print(f"   Issuer: {verification_result['issuer']}")
                print(f"   Recipient: {verification_result['recipient']}")
            else:
                print("❌ Certificate verification failed")
                print(f"   Error: {verification_result.get('error', 'Unknown error')}")
            
            return True
        else:
            print("❌ Certificate registration failed")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during certificate registration: {e}")
        return False

def test_blockchain_connection():
    """Test blockchain connection"""
    print("🌐 Testing Blockchain Connection")
    print("=" * 35)
    
    try:
        # Test connection
        balance = blockchain_service.get_account_balance()
        print(f"✅ Connected to SKALE Europa!")
        print(f"💰 Wallet Balance: {balance:.6f} sFUEL")
        
        # Test contract
        if blockchain_service.contract:
            print(f"✅ Contract loaded at: {blockchain_service.contract_address}")
            
            # Test contract function
            total_certs = blockchain_service.contract.functions.getTotalCertificates().call()
            print(f"📊 Total certificates on blockchain: {total_certs}")
            
            return True
        else:
            print("❌ Contract not loaded")
            return False
            
    except Exception as e:
        print(f"❌ Blockchain connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 SKALE Europa Certificate System Test")
    print("=" * 50)
    
    # Test 1: Blockchain connection
    if test_blockchain_connection():
        print("\n" + "="*50)
        
        # Test 2: Certificate registration
        if test_certificate_registration():
            print("\n🎉 All tests passed! Your certificate system is working perfectly on SKALE Europa!")
        else:
            print("\n❌ Certificate registration test failed")
    else:
        print("\n❌ Blockchain connection test failed")
