<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Enregistrement Certificat</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">
                Test - Enregistrement Blockchain
            </h1>
            
            <div id="status" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-blue-800">✅ Page chargée avec succès!</p>
            </div>
            
            <form id="testForm" method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="certificate_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nom du certificat *
                    </label>
                    <input 
                        type="text" 
                        id="certificate_name" 
                        name="certificate_name" 
                        required 
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Ex: Certificat de Formation Python"
                    >
                </div>
                
                <div>
                    <label for="issuer" class="block text-sm font-medium text-gray-700 mb-2">
                        Organisme émetteur *
                    </label>
                    <input 
                        type="text" 
                        id="issuer" 
                        name="issuer" 
                        required 
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Ex: Université de Paris"
                    >
                </div>
                
                <div>
                    <label for="issue_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Date d'émission
                    </label>
                    <input 
                        type="date" 
                        id="issue_date" 
                        name="issue_date" 
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                </div>
                
                <div>
                    <label for="certificate_file" class="block text-sm font-medium text-gray-700 mb-2">
                        Fichier du certificat *
                    </label>
                    <input 
                        type="file" 
                        id="certificate_file" 
                        name="certificate_file" 
                        required 
                        accept=".pdf,.jpg,.jpeg,.png"
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                </div>
                
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea 
                        id="description" 
                        name="description" 
                        rows="3"
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Description du certificat..."
                    ></textarea>
                </div>
                
                <button 
                    type="submit" 
                    class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                    Enregistrer sur la Blockchain
                </button>
            </form>
            
            <div id="result" class="mt-6 hidden">
                <!-- Results will be shown here -->
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const statusDiv = document.getElementById('status');
            const resultDiv = document.getElementById('result');
            
            statusDiv.innerHTML = '<p class="text-yellow-800">⏳ Envoi en cours...</p>';
            statusDiv.className = 'mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg';
            
            const formData = new FormData(this);
            
            fetch('/register-certificate/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = '<p class="text-green-800">✅ Certificat enregistré avec succès!</p>';
                    statusDiv.className = 'mb-6 p-4 bg-green-50 border border-green-200 rounded-lg';
                    
                    resultDiv.innerHTML = `
                        <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-gray-800 mb-2">Détails de l'enregistrement:</h3>
                            <p><strong>ID Certificat:</strong> ${data.certificate_id}</p>
                            <p><strong>Hash Transaction:</strong> ${data.transaction_hash}</p>
                            <p><strong>Hash Fichier:</strong> ${data.file_hash}</p>
                        </div>
                    `;
                    resultDiv.classList.remove('hidden');
                } else {
                    statusDiv.innerHTML = `<p class="text-red-800">❌ Erreur: ${data.error}</p>`;
                    statusDiv.className = 'mb-6 p-4 bg-red-50 border border-red-200 rounded-lg';
                }
            })
            .catch(error => {
                statusDiv.innerHTML = `<p class="text-red-800">❌ Erreur de connexion: ${error}</p>`;
                statusDiv.className = 'mb-6 p-4 bg-red-50 border border-red-200 rounded-lg';
            });
        });
    </script>
</body>
</html>
