#!/usr/bin/env python3
"""
Quick blockchain status check
"""
import os
import sys
import django
from web3 import Web3

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def quick_check():
    print("🚀 Quick Blockchain Status Check")
    print("=" * 50)
    
    # Connect to SKALE Europa
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if w3.is_connected():
        print(f"✅ Connected to SKALE Europa")
        print(f"   Latest Block: {w3.eth.block_number:,}")
        print(f"   Chain ID: {w3.eth.chain_id}")
        
        # Check wallet balance
        wallet_address = "******************************************"
        balance_wei = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance_wei, 'ether')
        print(f"   Wallet Balance: {balance_ether:.6f} sFUEL")
        
        # Check if we have a deployed contract
        try:
            from blockchain.blockchain_service import blockchain_service
            if hasattr(blockchain_service, 'contract_address') and blockchain_service.contract_address:
                print(f"   Contract Address: {blockchain_service.contract_address}")
                
                # Check if contract exists
                code = w3.eth.get_code(blockchain_service.contract_address)
                if code and code != '0x':
                    print(f"   Contract Status: ✅ Deployed and Active")
                    
                    # Try to get total certificates
                    if blockchain_service.contract:
                        try:
                            total = blockchain_service.contract.functions.getTotalCertificates().call()
                            print(f"   Total Certificates: {total}")
                        except Exception as e:
                            print(f"   Contract Call Error: {e}")
                else:
                    print(f"   Contract Status: ❌ No code at address")
            else:
                print(f"   Contract Status: ❌ No contract address configured")
                
        except Exception as e:
            print(f"   Blockchain Service Error: {e}")
        
        print(f"\n🌐 Explorer URL: https://elated-tan-skat.explorer.mainnet.skalenodes.com/")
        print(f"🔍 Search for your wallet: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{wallet_address}")
        
    else:
        print("❌ Failed to connect to SKALE Europa")

if __name__ == "__main__":
    quick_check()
