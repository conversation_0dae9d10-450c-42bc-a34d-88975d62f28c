#!/usr/bin/env python3
"""
Install and test Solidity compiler
"""
import os
import sys
from solcx import install_solc, set_solc_version, get_installed_solc_versions, compile_source

def install_and_test_solc():
    print("🔧 INSTALLING SOLIDITY COMPILER")
    print("=" * 40)
    
    try:
        # Check what's already installed
        installed_versions = get_installed_solc_versions()
        print(f"📋 Currently installed versions: {installed_versions}")
        
        # Install a stable version
        target_version = "0.8.19"  # Stable version
        print(f"\n⬇️ Installing Solidity {target_version}...")
        
        install_solc(target_version)
        print(f"✅ Solidity {target_version} installed successfully!")
        
        # Set as active version
        set_solc_version(target_version)
        print(f"🎯 Set {target_version} as active version")
        
        # Test compilation with simple contract
        print(f"\n🧪 Testing compilation...")
        
        test_contract = '''
        pragma solidity ^0.8.19;
        
        contract SimpleTest {
            uint256 public value;
            
            function setValue(uint256 _value) public {
                value = _value;
            }
            
            function getValue() public view returns (uint256) {
                return value;
            }
        }
        '''
        
        compiled_sol = compile_source(test_contract)
        contract_interface = compiled_sol['<stdin>:SimpleTest']
        
        print(f"✅ Compilation test successful!")
        print(f"📝 ABI items: {len(contract_interface['abi'])}")
        print(f"💾 Bytecode length: {len(contract_interface['bin'])} chars")
        
        return True
        
    except Exception as e:
        print(f"❌ Error installing/testing Solc: {e}")
        return False

def check_environment():
    print("\n🔍 CHECKING ENVIRONMENT")
    print("=" * 30)
    
    # Check Python version
    print(f"🐍 Python version: {sys.version}")
    
    # Check if we're in virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
    else:
        print("⚠️ Not in virtual environment (recommended to use venv)")
    
    # Check solcx installation
    try:
        import solcx
        try:
            version = solcx.__version__
            print(f"✅ solcx module available: {version}")
        except AttributeError:
            print("✅ solcx module available (version unknown)")
    except ImportError:
        print("❌ solcx not installed. Run: pip install py-solc-x")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 SOLIDITY COMPILER SETUP")
    print("=" * 50)
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed")
        sys.exit(1)
    
    # Install and test Solc
    if install_and_test_solc():
        print("\n🎉 SUCCESS! Solidity compiler is ready!")
        print("\n📋 NEXT STEPS:")
        print("1. Now you can run contract deployment scripts")
        print("2. They will compile contracts from source instead of using pre-compiled bytecode")
        print("3. This should resolve the gas estimation issues")
    else:
        print("\n❌ FAILED! Check the errors above")
        sys.exit(1)
