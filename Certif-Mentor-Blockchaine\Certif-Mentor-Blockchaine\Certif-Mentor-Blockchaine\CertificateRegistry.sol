// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title CertificateRegistry
 * @dev Smart contract for registering and verifying certificates on SKALE Europa
 */
contract CertificateRegistry {
    
    struct Certificate {
        string name;                // Certificate name
        string issuer;             // Issuing organization
        string recipient;          // Certificate recipient
        uint256 issueDate;         // Issue date (timestamp)
        bytes32 fileHash;          // SHA-256 hash of certificate file (optimized)
        address registeredBy;      // Address that registered the certificate
        uint256 registrationDate;  // When it was registered on blockchain
        bool exists;               // Whether certificate exists
    }
    
    // Mapping from certificate ID to certificate data
    mapping(bytes32 => Certificate) public certificates;

    // Mapping from file hash to certificate ID (prevents duplicates)
    mapping(bytes32 => bytes32) public hashToCertificateId;

    // Counter for total certificates (replaces array)
    uint256 public totalCertificates;
    
    // Events
    event CertificateRegistered(
        bytes32 indexed certificateId,
        string name,
        string issuer,
        string recipient,
        address registeredBy
    );
    
    /**
     * @dev Register a new certificate on the blockchain
     * @param _name Certificate name
     * @param _issuer Issuing organization
     * @param _recipient Certificate recipient
     * @param _issueDate Issue date (timestamp)
     * @param _fileHash SHA-256 hash of the certificate file
     * @return certificateId Unique identifier for the certificate
     */
    function registerCertificate(
        string memory _name,
        string memory _issuer,
        string memory _recipient,
        uint256 _issueDate,
        bytes32 _fileHash
    ) public returns (bytes32) {

        // Check if certificate with this file hash already exists
        require(hashToCertificateId[_fileHash] == bytes32(0), "Certificate with this file hash already registered");

        // Generate unique certificate ID
        bytes32 certificateId = keccak256(
            abi.encodePacked(
                _name,
                _issuer,
                _recipient,
                _issueDate,
                _fileHash,
                block.timestamp,
                msg.sender
            )
        );

        // Create certificate struct
        certificates[certificateId] = Certificate({
            name: _name,
            issuer: _issuer,
            recipient: _recipient,
            issueDate: _issueDate,
            fileHash: _fileHash,
            registeredBy: msg.sender,
            registrationDate: block.timestamp,
            exists: true
        });

        // Map file hash to certificate ID
        hashToCertificateId[_fileHash] = certificateId;

        // Increment total certificates counter
        totalCertificates++;
        
        // Emit event
        emit CertificateRegistered(
            certificateId,
            _name,
            _issuer,
            _recipient,
            msg.sender
        );
        
        return certificateId;
    }
    
    /**
     * @dev Get certificate by its ID
     * @param _certificateId Certificate ID to get
     * @return name Certificate name
     * @return issuer Certificate issuer
     * @return recipient Certificate recipient
     * @return issueDate Certificate issue date
     * @return fileHash Certificate file hash
     * @return registeredBy Address that registered the certificate
     * @return registrationDate Registration timestamp
     * @return exists Whether certificate exists
     */
    function getCertificateById(bytes32 _certificateId)
        public
        view
        returns (
            string memory name,
            string memory issuer,
            string memory recipient,
            uint256 issueDate,
            bytes32 fileHash,
            address registeredBy,
            uint256 registrationDate,
            bool exists
        )
    {
        Certificate memory cert = certificates[_certificateId];
        return (
            cert.name,
            cert.issuer,
            cert.recipient,
            cert.issueDate,
            cert.fileHash,
            cert.registeredBy,
            cert.registrationDate,
            cert.exists
        );
    }
    


    /**
     * @dev Verify a certificate by its file hash
     * @param _fileHash SHA-256 hash of certificate file
     * @return certificateId Certificate ID
     * @return name Certificate name
     * @return issuer Certificate issuer
     * @return recipient Certificate recipient
     * @return issueDate Certificate issue date
     * @return registeredBy Address that registered the certificate
     * @return registrationDate Registration timestamp
     * @return exists Whether certificate exists
     */
    function verifyCertificateByHash(bytes32 _fileHash)
        public
        view
        returns (
            bytes32 certificateId,
            string memory name,
            string memory issuer,
            string memory recipient,
            uint256 issueDate,
            address registeredBy,
            uint256 registrationDate,
            bool exists
        )
    {
        bytes32 certId = hashToCertificateId[_fileHash];
        if (certId == bytes32(0)) {
            return (bytes32(0), "", "", "", 0, address(0), 0, false);
        }

        Certificate memory cert = certificates[certId];
        return (
            certId,
            cert.name,
            cert.issuer,
            cert.recipient,
            cert.issueDate,
            cert.registeredBy,
            cert.registrationDate,
            cert.exists
        );
    }
    
    /**
     * @dev Get total number of registered certificates
     * @return Total count of certificates
     */
    function getTotalCertificates() public view returns (uint256) {
        return totalCertificates;
    }
    
    /**
     * @dev Check if a certificate exists
     * @param _certificateId Certificate ID to check
     * @return Whether certificate exists
     */
    function certificateExists(bytes32 _certificateId) public view returns (bool) {
        return certificates[_certificateId].exists;
    }
    

}
