from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth.models import User
from .models import UserProfile

@login_required
def profile_view(request):
    """Display user profile"""

    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    context = {
        'user': request.user,
        'profile': profile,
    }

    return render(request, 'users/profile.html', context)

@login_required
def edit_profile(request):
    """Edit user profile"""

    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update User model fields
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        # Update UserProfile fields
        profile.bio = request.POST.get('bio', '')
        profile.phone = request.POST.get('phone', '')
        profile.profession = request.POST.get('profession', '')
        profile.experience_level = request.POST.get('experience_level', 'beginner')
        profile.interests = request.POST.get('interests', '')
        profile.linkedin_url = request.POST.get('linkedin_url', '')
        profile.github_url = request.POST.get('github_url', '')
        profile.twitter_url = request.POST.get('twitter_url', '')
        profile.website_url = request.POST.get('website_url', '')

        # Handle profile image upload
        if 'profile_image' in request.FILES:
            profile.profile_image = request.FILES['profile_image']

        # Handle checkboxes
        profile.email_notifications = 'email_notifications' in request.POST
        profile.public_profile = 'public_profile' in request.POST

        # Handle birth_date
        birth_date = request.POST.get('birth_date')
        if birth_date:
            try:
                from datetime import datetime
                profile.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
            except ValueError:
                profile.birth_date = None
        else:
            profile.birth_date = None

        profile.save()

        messages.success(request, 'Votre profil a été mis à jour avec succès!')
        return redirect('users:profile')

    context = {
        'user': request.user,
        'profile': profile,
    }

    return render(request, 'users/edit_profile.html', context)
