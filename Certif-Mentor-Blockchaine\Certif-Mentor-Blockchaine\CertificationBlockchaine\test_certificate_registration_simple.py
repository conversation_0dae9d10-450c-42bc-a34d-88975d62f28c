#!/usr/bin/env python3
"""
Test certificate registration with the updated blockchain service
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CertificationBlockchaine.settings')
django.setup()

def test_certificate_registration():
    print("🧪 Testing Certificate Registration")
    print("=" * 40)
    
    try:
        from blockchain.blockchain_service import blockchain_service
        from datetime import datetime
        
        # Test data
        certificate_data = {
            'name': 'Test Certificate',
            'issuer': 'Test University',
            'recipient': 'Test Student',
            'issue_date': datetime.now().timestamp(),
            'certificate_number': 'TEST-001',
            'description': 'Test certificate for blockchain registration'
        }
        
        # Test file content
        file_content = b"This is a test certificate file content"
        
        print("📝 Registering test certificate...")
        result = blockchain_service.register_certificate(certificate_data, file_content)
        
        if result['success']:
            print("✅ Certificate registration successful!")
            print(f"   Certificate ID: {result['certificate_id']}")
            print(f"   Transaction Hash: {result['transaction_hash']}")
            print(f"   File Hash: {result['file_hash']}")
            print(f"   Mode: {result.get('mode', 'blockchain')}")
            return True
        else:
            print(f"❌ Certificate registration failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_certificate_registration()
