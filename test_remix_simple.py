#!/usr/bin/env python3
"""
Simple test of Remix bytecode
"""
print("🚀 TESTING REMIX BYTECODE")
print("=" * 40)

try:
    from web3 import Web3
    print("✅ Web3 imported successfully")
    
    # Test connection
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    print(f"✅ Web3 provider created")
    
    if w3.is_connected():
        print(f"✅ Connected to SKALE Europa")
        print(f"📊 Latest block: {w3.eth.block_number}")
    else:
        print("❌ Failed to connect")
        exit(1)
    
    # Test bytecode
    remix_bytecode = "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"
    
    print(f"✅ Remix bytecode loaded")
    print(f"💾 Bytecode length: {len(remix_bytecode)} chars")
    
    # Compare with our previous bytecode length
    print(f"📊 Previous local bytecode was ~5400 chars")
    print(f"📊 Remix bytecode is {len(remix_bytecode)} chars")
    
    if len(remix_bytecode) < 5000:
        print("🎉 Remix bytecode is SHORTER - this is good!")
    else:
        print("⚠️ Remix bytecode is similar length")
    
    print(f"\n✅ REMIX BYTECODE TEST COMPLETED!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
