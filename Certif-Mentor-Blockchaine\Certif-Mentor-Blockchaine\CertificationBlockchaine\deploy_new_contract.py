#!/usr/bin/env python3
"""
Simple script to deploy a new CertificateRegistry contract to SKALE Europa
"""
import json
import os
from web3 import Web3
from eth_account import Account
from solcx import compile_source, install_solc

def deploy_new_contract():
    """Deploy a fresh contract to SKALE Europa"""
    print("🚀 Deploying NEW CertificateRegistry to SKALE Europa...")
    print("=" * 60)
    
    # SKALE Europa configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    mnemonic = "path result hero width mention truck anger broom taxi pottery camera belt"
    
    # Initialize Web3 with timeout
    print("🌐 Connecting to SKALE Europa...")
    provider = Web3.HTTPProvider(
        rpc_url,
        request_kwargs={
            'timeout': 60,
            'headers': {
                'Content-Type': 'application/json',
                'User-Agent': 'CertificationBlockchain/1.0'
            }
        }
    )
    w3 = Web3(provider)
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False
    
    print(f"✅ Connected! Latest block: {w3.eth.block_number}")
    
    # Generate account from mnemonic
    print("🔑 Loading wallet...")
    try:
        Account.enable_unaudited_hdwallet_features()
        account = Account.from_mnemonic(mnemonic)
        wallet_address = account.address
        private_key = account.key.hex()
        print(f"✅ Wallet loaded: {wallet_address}")
    except Exception as e:
        print(f"❌ Failed to load wallet: {e}")
        return False
    
    # Check balance
    balance_wei = w3.eth.get_balance(wallet_address)
    balance_ether = w3.from_wei(balance_wei, 'ether')
    print(f"💰 Balance: {balance_ether:.6f} sFUEL")
    
    if balance_ether < 0.001:
        print("⚠️  Warning: Low balance!")
    
    # Install Solidity compiler
    print("🔨 Installing Solidity compiler...")
    try:
        install_solc('0.8.19')
        print("✅ Solidity compiler ready")
    except Exception as e:
        print(f"⚠️  Compiler warning: {e}")
    
    # Read contract source
    contract_path = os.path.join(os.path.dirname(__file__), '..', 'Certif-Mentor-Blockchaine', 'CertificateRegistry.sol')
    print("📄 Reading contract source...")
    
    try:
        with open(contract_path, 'r') as f:
            contract_source = f.read()
        print("✅ Contract source loaded")
    except FileNotFoundError:
        print(f"❌ Contract file not found: {contract_path}")
        return False
    
    # Compile contract
    print("🔨 Compiling contract...")
    try:
        compiled_sol = compile_source(
            contract_source,
            output_values=['abi', 'bin'],
            solc_version='0.8.19'
        )
        
        contract_id, contract_interface = compiled_sol.popitem()
        abi = contract_interface['abi']
        bytecode = contract_interface['bin']
        print("✅ Contract compiled successfully")
    except Exception as e:
        print(f"❌ Compilation failed: {e}")
        return False
    
    # Deploy contract
    print("🚀 Deploying contract...")
    try:
        # Create contract instance
        contract = w3.eth.contract(abi=abi, bytecode=bytecode)
        
        # Get gas estimate
        gas_estimate = contract.constructor().estimate_gas()
        print(f"⛽ Estimated gas: {gas_estimate}")
        
        # Build transaction
        transaction = contract.constructor().build_transaction({
            'chainId': chain_id,
            'gas': gas_estimate + 100000,  # Add buffer
            'gasPrice': w3.eth.gas_price,
            'nonce': w3.eth.get_transaction_count(wallet_address),
        })
        
        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key)
        
        # Send transaction
        print("📤 Sending deployment transaction...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        print(f"📋 Transaction hash: {tx_hash.hex()}")
        
        # Wait for receipt
        print("⏳ Waiting for confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if tx_receipt.status == 1:
            contract_address = tx_receipt.contractAddress
            print("✅ Contract deployed successfully!")
            print(f"📍 Contract Address: {contract_address}")
            print(f"⛽ Gas Used: {tx_receipt.gasUsed}")
            print(f"🔗 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/address/{contract_address}")
            
            # Save ABI
            abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
            with open(abi_path, 'w') as f:
                json.dump(abi, f, indent=2)
            print(f"💾 ABI saved to: {abi_path}")
            
            # Update blockchain service with new address
            update_blockchain_service(contract_address)
            
            return contract_address
        else:
            print("❌ Deployment failed!")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

def update_blockchain_service(contract_address):
    """Update the blockchain service with the new contract address"""
    print("🔄 Updating blockchain service...")
    
    service_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'blockchain_service.py')
    
    try:
        # Read current file
        with open(service_path, 'r') as f:
            content = f.read()
        
        # Replace the contract address
        old_line = 'self.contract_address = "0x0cca0FC725989DeD52e3FD1c11A76c817562d757"'
        new_line = f'self.contract_address = "{contract_address}"'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
        else:
            # Try alternative pattern
            import re
            pattern = r'self\.contract_address = "[^"]*"'
            content = re.sub(pattern, f'self.contract_address = "{contract_address}"', content)
        
        # Write updated file
        with open(service_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Blockchain service updated with new address: {contract_address}")
        
    except Exception as e:
        print(f"⚠️  Could not auto-update blockchain service: {e}")
        print(f"📝 Please manually update contract_address to: {contract_address}")

if __name__ == "__main__":
    result = deploy_new_contract()
    if result:
        print("\n🎉 SUCCESS! New contract deployed and ready to use!")
        print("=" * 60)
        print("Next steps:")
        print("1. Restart your Django server")
        print("2. Test certificate registration")
    else:
        print("\n❌ Deployment failed!")
