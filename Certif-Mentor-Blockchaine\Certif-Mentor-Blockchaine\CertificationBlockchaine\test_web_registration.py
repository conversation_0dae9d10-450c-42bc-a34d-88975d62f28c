#!/usr/bin/env python3
"""
Test certificate registration through the web interface
"""
import requests
import io
from datetime import datetime

def test_web_registration():
    print("🌐 Testing Web Certificate Registration")
    print("=" * 45)
    
    # Test data
    data = {
        'certificate_name': 'Test Web Certificate',
        'issuer': 'Test University Web',
        'issue_date': '2024-01-15',
        'certificate_number': 'WEB-001',
        'description': 'Test certificate via web interface'
    }
    
    # Create a test file
    test_file_content = b"This is a test certificate file for web upload"
    files = {
        'certificate_file': ('test_certificate.pdf', io.BytesIO(test_file_content), 'application/pdf')
    }
    
    try:
        # First, get the CSRF token
        print("🔐 Getting CSRF token...")
        session = requests.Session()
        response = session.get('http://localhost:8000/register-certificate/')
        
        if response.status_code != 200:
            print(f"❌ Failed to get registration page: {response.status_code}")
            return False
        
        # Extract CSRF token from the response
        csrf_token = None
        for line in response.text.split('\n'):
            if 'csrfmiddlewaretoken' in line and 'value=' in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break
        
        if not csrf_token:
            print("❌ Could not find CSRF token")
            return False
        
        print(f"✅ CSRF token obtained: {csrf_token[:20]}...")
        
        # Add CSRF token to data
        data['csrfmiddlewaretoken'] = csrf_token
        
        # Submit the form
        print("📤 Submitting certificate registration...")
        response = session.post(
            'http://localhost:8000/register-certificate/',
            data=data,
            files=files,
            headers={
                'X-CSRFToken': csrf_token,
                'Referer': 'http://localhost:8000/register-certificate/'
            }
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✅ Certificate registration successful!")
                    print(f"   Certificate ID: {result.get('certificate_id', 'N/A')}")
                    print(f"   Transaction Hash: {result.get('transaction_hash', 'N/A')}")
                    print(f"   File Hash: {result.get('file_hash', 'N/A')}")
                    return True
                else:
                    print(f"❌ Registration failed: {result.get('error', 'Unknown error')}")
                    return False
            except Exception as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Response content: {response.text[:200]}...")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_web_registration()
