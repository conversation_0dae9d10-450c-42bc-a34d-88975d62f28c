#!/usr/bin/env python3
"""
Test script to verify the deployed smart contract on SKALE Europa
"""
import json
import os
from web3 import Web3
from eth_account import Account

def test_contract():
    print("🧪 Testing CertificateRegistry on SKALE Europa")
    print("=" * 50)
    
    # SKALE Europa configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    contract_address = "******************************************"
    
    # Initialize Web3
    print("🌐 Connecting to SKALE Europa...")
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    if not w3.is_connected():
        print("❌ Failed to connect to SKALE Europa")
        return False
    
    print("✅ Connected to SKALE Europa!")
    
    # Load contract ABI
    abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
    try:
        with open(abi_path, 'r') as f:
            contract_abi = json.load(f)
        print("✅ Contract ABI loaded")
    except FileNotFoundError:
        print(f"❌ ABI file not found: {abi_path}")
        return False
    
    # Create contract instance
    contract = w3.eth.contract(address=contract_address, abi=contract_abi)
    print(f"✅ Contract instance created: {contract_address}")
    
    # Test contract functions
    print("\n📋 Testing contract functions...")
    
    try:
        # Test getTotalCertificates
        total_certs = contract.functions.getTotalCertificates().call()
        print(f"📊 Total certificates registered: {total_certs}")
        
        # Test if contract exists (by calling a view function)
        print("✅ Contract is accessible and functional!")
        
        # Check wallet balance
        wallet_address = "******************************************"
        balance = w3.eth.get_balance(wallet_address)
        balance_ether = w3.from_wei(balance, 'ether')
        print(f"💰 Wallet balance: {balance_ether:.6f} sFUEL")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing contract: {e}")
        return False

def test_certificate_registration():
    """Test registering a certificate"""
    print("\n🧪 Testing Certificate Registration")
    print("=" * 40)
    
    # SKALE Europa configuration
    rpc_url = "https://mainnet.skalenodes.com/v1/elated-tan-skat"
    chain_id = **********
    contract_address = "******************************************"
    wallet_address = "******************************************"
    mnemonic = "path result hero width mention truck anger broom taxi pottery camera belt"
    
    # Generate private key from mnemonic
    Account.enable_unaudited_hdwallet_features()
    account = Account.from_mnemonic(mnemonic)
    private_key = account.key.hex()
    
    # Initialize Web3
    w3 = Web3(Web3.HTTPProvider(rpc_url))
    
    # Load contract ABI
    abi_path = os.path.join(os.path.dirname(__file__), 'blockchain', 'contract_abi.json')
    with open(abi_path, 'r') as f:
        contract_abi = json.load(f)
    
    # Create contract instance
    contract = w3.eth.contract(address=contract_address, abi=contract_abi)
    
    # Test certificate data
    test_cert = {
        'name': 'Test Certificate - Blockchain Verification',
        'issuer': 'SKALE Europa Test Authority',
        'recipient': 'Test User',
        'issue_date': **********,  # January 1, 2025
        'certificate_number': 'TEST-001',
        'description': 'This is a test certificate to verify blockchain functionality',
        'file_hash': 'test_hash_' + str(int(w3.eth.get_block('latest').timestamp))
    }
    
    try:
        print("📝 Preparing test certificate registration...")
        
        # Get nonce and gas price
        nonce = w3.eth.get_transaction_count(wallet_address)
        gas_price = w3.eth.gas_price
        
        # Build transaction
        transaction = contract.functions.registerCertificate(
            test_cert['name'],
            test_cert['issuer'],
            test_cert['recipient'],
            test_cert['issue_date'],
            test_cert['certificate_number'],
            test_cert['description'],
            test_cert['file_hash']
        ).build_transaction({
            'chainId': chain_id,
            'gas': 2000000,
            'gasPrice': gas_price,
            'nonce': nonce,
            'from': wallet_address
        })
        
        # Sign transaction
        signed_txn = w3.eth.account.sign_transaction(transaction, private_key=private_key)
        
        # Send transaction
        print("📤 Sending transaction...")
        tx_hash = w3.eth.send_raw_transaction(signed_txn.raw_transaction)
        print(f"📤 Transaction sent: {tx_hash.hex()}")
        
        # Wait for receipt
        print("⏳ Waiting for confirmation...")
        tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
        
        print(f"✅ Certificate registered successfully!")
        print(f"   📍 Transaction Hash: {tx_hash.hex()}")
        print(f"   ⛽ Gas Used: {tx_receipt.gasUsed}")
        print(f"   🌐 Explorer: https://elated-tan-skat.explorer.mainnet.skalenodes.com/tx/{tx_hash.hex()}")
        
        # Extract certificate ID from logs
        if tx_receipt.logs:
            event_log = contract.events.CertificateRegistered().process_log(tx_receipt.logs[0])
            certificate_id = event_log['args']['certificateId'].hex()
            print(f"   🆔 Certificate ID: {certificate_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error registering certificate: {e}")
        return False

if __name__ == "__main__":
    print("🚀 SKALE Europa Blockchain Test Suite")
    print("=" * 50)
    
    # Test 1: Contract connectivity
    if test_contract():
        print("\n✅ Contract test passed!")
        
        # Test 2: Certificate registration
        user_input = input("\n🤔 Do you want to test certificate registration? (y/n): ")
        if user_input.lower() == 'y':
            if test_certificate_registration():
                print("\n🎉 All tests passed! Your blockchain system is working perfectly!")
            else:
                print("\n❌ Certificate registration test failed")
        else:
            print("\n✅ Contract connectivity test completed successfully!")
    else:
        print("\n❌ Contract test failed")
